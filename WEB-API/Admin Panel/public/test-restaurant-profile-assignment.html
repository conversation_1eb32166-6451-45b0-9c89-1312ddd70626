<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="test-token">
    <title>Test Restaurant Profile Assignment</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-section { margin: 2rem 0; padding: 1.5rem; border: 1px solid #dee2e6; border-radius: 0.5rem; }
        .result-box { margin-top: 1rem; padding: 1rem; border-radius: 0.25rem; }
        .success { background-color: #d1edff; border: 1px solid #0ea5e9; }
        .error { background-color: #fee2e2; border: 1px solid #ef4444; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-utensils me-2"></i>
            اختبار تعيين ملفات العمولة للمطاعم
        </h1>

        <!-- Individual Restaurant Assignment Test -->
        <div class="test-section">
            <h3><i class="fas fa-restaurant me-2"></i>تعيين ملف عمولة لمطعم واحد</h3>
            <div class="row">
                <div class="col-md-4">
                    <label class="form-label">اختر المطعم:</label>
                    <select class="form-select" id="singleRestaurant">
                        <option value="">جاري التحميل...</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">اختر ملف العمولة:</label>
                    <select class="form-select" id="singleProfile">
                        <option value="">جاري التحميل...</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <button class="btn btn-primary w-100" onclick="assignSingleProfile()">
                        <i class="fas fa-check me-2"></i>تعيين الملف
                    </button>
                </div>
            </div>
            <div id="singleResult" class="result-box" style="display: none;"></div>
        </div>

        <!-- Bulk Assignment Test -->
        <div class="test-section">
            <h3><i class="fas fa-layer-group me-2"></i>تعيين ملف عمولة لعدة مطاعم</h3>
            <div class="row">
                <div class="col-md-6">
                    <label class="form-label">اختر المطاعم (متعددة):</label>
                    <select class="form-select" id="bulkRestaurants" multiple size="5">
                        <option value="">جاري التحميل...</option>
                    </select>
                    <small class="text-muted">اضغط Ctrl للاختيار المتعدد</small>
                </div>
                <div class="col-md-3">
                    <label class="form-label">اختر ملف العمولة:</label>
                    <select class="form-select" id="bulkProfile">
                        <option value="">جاري التحميل...</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <button class="btn btn-success w-100" onclick="assignBulkProfiles()">
                        <i class="fas fa-tasks me-2"></i>تعيين للكل
                    </button>
                </div>
            </div>
            <div id="bulkResult" class="result-box" style="display: none;"></div>
        </div>

        <!-- Current Status -->
        <div class="test-section">
            <h3><i class="fas fa-chart-bar me-2"></i>الحالة الحالية</h3>
            <div class="row">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-primary" id="totalRestaurants">-</h5>
                            <p class="card-text">إجمالي المطاعم</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success" id="withProfiles">-</h5>
                            <p class="card-text">مع ملفات عمولة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning" id="withoutProfiles">-</h5>
                            <p class="card-text">بدون ملفات عمولة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-info" id="totalProfiles">-</h5>
                            <p class="card-text">ملفات العمولة المتاحة</p>
                        </div>
                    </div>
                </div>
            </div>
            <button class="btn btn-outline-secondary mt-3" onclick="loadData()">
                <i class="fas fa-sync me-2"></i>تحديث البيانات
            </button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let restaurants = [];
        let profiles = [];

        // Load initial data
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
        });

        async function loadData() {
            try {
                // Load restaurants
                const restaurantsResponse = await fetch('/api/unified-pricing/restaurants');
                const restaurantsData = await restaurantsResponse.json();
                restaurants = restaurantsData.data || [];

                // Load commission profiles
                const profilesResponse = await fetch('/api/unified-pricing/commission-profiles');
                const profilesData = await profilesResponse.json();
                profiles = profilesData.data || [];

                populateSelects();
                updateStats();
            } catch (error) {
                console.error('Error loading data:', error);
                showResult('singleResult', 'خطأ في تحميل البيانات: ' + error.message, 'error');
            }
        }

        function populateSelects() {
            // Populate restaurant selects
            const singleSelect = document.getElementById('singleRestaurant');
            const bulkSelect = document.getElementById('bulkRestaurants');
            
            singleSelect.innerHTML = '<option value="">اختر المطعم</option>';
            bulkSelect.innerHTML = '';

            restaurants.forEach(restaurant => {
                const option1 = document.createElement('option');
                option1.value = restaurant.restaurant_id;
                option1.textContent = restaurant.restaurant_name + (restaurant.has_profile ? ' (له ملف)' : ' (بدون ملف)');
                singleSelect.appendChild(option1);

                const option2 = document.createElement('option');
                option2.value = restaurant.restaurant_id;
                option2.textContent = restaurant.restaurant_name + (restaurant.has_profile ? ' (له ملف)' : ' (بدون ملف)');
                bulkSelect.appendChild(option2);
            });

            // Populate profile selects
            const singleProfileSelect = document.getElementById('singleProfile');
            const bulkProfileSelect = document.getElementById('bulkProfile');
            
            singleProfileSelect.innerHTML = '<option value="">اختر ملف العمولة</option>';
            bulkProfileSelect.innerHTML = '<option value="">اختر ملف العمولة</option>';

            profiles.forEach(profile => {
                const option1 = document.createElement('option');
                option1.value = profile.id;
                option1.textContent = `${profile.display_name} (${profile.profile_type})`;
                singleProfileSelect.appendChild(option1);

                const option2 = document.createElement('option');
                option2.value = profile.id;
                option2.textContent = `${profile.display_name} (${profile.profile_type})`;
                bulkProfileSelect.appendChild(option2);
            });
        }

        function updateStats() {
            const total = restaurants.length;
            const withProfiles = restaurants.filter(r => r.has_profile).length;
            const withoutProfiles = total - withProfiles;

            document.getElementById('totalRestaurants').textContent = total;
            document.getElementById('withProfiles').textContent = withProfiles;
            document.getElementById('withoutProfiles').textContent = withoutProfiles;
            document.getElementById('totalProfiles').textContent = profiles.length;
        }

        async function assignSingleProfile() {
            const restaurantId = document.getElementById('singleRestaurant').value;
            const profileId = document.getElementById('singleProfile').value;

            if (!restaurantId || !profileId) {
                showResult('singleResult', 'يرجى اختيار المطعم وملف العمولة', 'error');
                return;
            }

            try {
                const response = await fetch('/api/unified-pricing/restaurants/assign-profile', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        restaurant_id: restaurantId,
                        commission_profile_id: profileId
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    showResult('singleResult', 'تم تعيين ملف العمولة بنجاح!', 'success');
                    loadData(); // Refresh data
                } else {
                    showResult('singleResult', 'فشل في التعيين: ' + result.message, 'error');
                }
            } catch (error) {
                showResult('singleResult', 'خطأ في الشبكة: ' + error.message, 'error');
            }
        }

        async function assignBulkProfiles() {
            const restaurantIds = Array.from(document.getElementById('bulkRestaurants').selectedOptions).map(option => option.value);
            const profileId = document.getElementById('bulkProfile').value;

            if (restaurantIds.length === 0 || !profileId) {
                showResult('bulkResult', 'يرجى اختيار المطاعم وملف العمولة', 'error');
                return;
            }

            try {
                const response = await fetch('/api/unified-pricing/restaurants/bulk-operations', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        operation: 'assign_profile',
                        restaurant_ids: restaurantIds,
                        profile_id: profileId,
                        reason: 'Bulk assignment test'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    showResult('bulkResult', `تم تعيين ملف العمولة لـ ${result.results.success} مطعم بنجاح!`, 'success');
                    loadData(); // Refresh data
                } else {
                    showResult('bulkResult', 'فشل في التعيين المجمع: ' + result.message, 'error');
                }
            } catch (error) {
                showResult('bulkResult', 'خطأ في الشبكة: ' + error.message, 'error');
            }
        }

        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.className = `result-box ${type}`;
            element.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>${message}`;
            element.style.display = 'block';
        }
    </script>
</body>
</html>
