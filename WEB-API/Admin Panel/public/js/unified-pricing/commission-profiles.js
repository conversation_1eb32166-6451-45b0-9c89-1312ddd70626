/**
 * Commission Profiles Management
 * Simplified commission profile management system
 */
class CommissionProfilesManagement {
    constructor() {
        this.profiles = [];
        this.filteredProfiles = [];
        this.currentEditId = null;
        this.init();
    }

    init() {
        this.loadProfiles();
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Form validation
        $('#profileForm').on('input', 'input[required]', this.validateForm.bind(this));
    }

    async loadProfiles() {
        try {
            this.showLoading(true);
            
            const response = await fetch('/api/unified-pricing/commission-profiles', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            const result = await response.json();
            
            if (result.success) {
                this.profiles = result.data || [];
                this.filteredProfiles = [...this.profiles];
                this.renderProfilesTable();
            } else {
                this.showError('فشل في تحميل ملفات العمولة: ' + result.message);
            }
        } catch (error) {
            console.error('Error loading profiles:', error);
            this.showError('حدث خطأ أثناء تحميل ملفات العمولة');
        } finally {
            this.showLoading(false);
        }
    }

    renderProfilesTable() {
        const tbody = document.getElementById('profilesTableBody');
        const emptyState = document.getElementById('emptyState');
        
        if (this.filteredProfiles.length === 0) {
            tbody.innerHTML = '';
            emptyState.style.display = 'block';
            return;
        }

        emptyState.style.display = 'none';
        
        tbody.innerHTML = this.filteredProfiles.map(profile => `
            <tr>
                <td>
                    <strong>${profile.display_name}</strong>
                    ${profile.description ? `<br><small class="text-muted">${profile.description}</small>` : ''}
                </td>
                <td>
                    <span class="badge bg-primary profile-type-badge">
                        ${this.getProfileTypeLabel(profile.profile_type)}
                    </span>
                </td>
                <td class="commission-rate">${profile.admin_commission_rate}%</td>
                <td class="commission-rate">${profile.driver_commission_rate}%</td>
                <td class="delivery-charge">${profile.base_delivery_charge} ر.س</td>
                <td class="restaurants-count">${profile.restaurants_count || 0}</td>
                <td>
                    <span class="badge ${profile.is_active ? 'bg-success' : 'bg-danger'}">
                        ${profile.is_active ? 'نشط' : 'غير نشط'}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-primary" 
                                onclick="commissionProfiles.editProfile('${profile.id}')"
                                title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-outline-info" 
                                onclick="commissionProfiles.viewProfile('${profile.id}')"
                                title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger" 
                                onclick="commissionProfiles.deleteProfile('${profile.id}')"
                                title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    showCreateModal() {
        this.currentEditId = null;
        document.getElementById('profileModalTitle').textContent = 'إنشاء ملف عمولة جديد';
        document.getElementById('submitButtonText').textContent = 'إنشاء الملف';
        document.getElementById('profileForm').reset();
        $('#profileModal').modal('show');
    }

    async editProfile(profileId) {
        try {
            const profile = this.profiles.find(p => p.id === profileId);
            if (!profile) {
                this.showError('الملف غير موجود');
                return;
            }

            this.currentEditId = profileId;
            document.getElementById('profileModalTitle').textContent = 'تعديل ملف العمولة';
            document.getElementById('submitButtonText').textContent = 'حفظ التغييرات';

            // Fill form with profile data
            document.getElementById('profileId').value = profile.id;
            document.getElementById('display_name').value = profile.display_name;
            document.getElementById('profile_type').value = profile.profile_type;
            document.getElementById('description').value = profile.description || '';
            document.getElementById('admin_commission_rate').value = profile.admin_commission_rate;
            document.getElementById('driver_commission_rate').value = profile.driver_commission_rate;
            document.getElementById('base_delivery_charge').value = profile.base_delivery_charge;
            document.getElementById('per_km_delivery_rate').value = profile.per_km_delivery_rate;
            document.getElementById('is_active').checked = profile.is_active;

            $('#profileModal').modal('show');
        } catch (error) {
            console.error('Error editing profile:', error);
            this.showError('حدث خطأ أثناء تحميل بيانات الملف');
        }
    }

    async handleSubmit(event) {
        event.preventDefault();
        
        if (!this.validateForm()) {
            return;
        }

        try {
            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData.entries());
            
            // Convert checkbox to boolean
            data.is_active = document.getElementById('is_active').checked;
            
            // Generate internal name from display name
            data.name = data.display_name.toLowerCase().replace(/\s+/g, '_').replace(/[^\w_]/g, '');
            data.business_model = 'percentage'; // Simplified to percentage only

            const url = this.currentEditId 
                ? `/api/unified-pricing/commission-profiles/${this.currentEditId}`
                : '/api/unified-pricing/commission-profiles';
            
            const method = this.currentEditId ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();
            
            if (result.success) {
                this.showSuccess(this.currentEditId ? 'تم تحديث الملف بنجاح' : 'تم إنشاء الملف بنجاح');
                $('#profileModal').modal('hide');
                this.loadProfiles();
            } else {
                this.showError('فشل في حفظ الملف: ' + result.message);
            }
        } catch (error) {
            console.error('Error saving profile:', error);
            this.showError('حدث خطأ أثناء حفظ الملف');
        }
    }

    deleteProfile(profileId) {
        const profile = this.profiles.find(p => p.id === profileId);
        if (!profile) {
            this.showError('الملف غير موجود');
            return;
        }

        this.currentEditId = profileId;
        document.getElementById('deleteProfileName').textContent = profile.display_name;
        $('#deleteModal').modal('show');
    }

    async confirmDelete() {
        try {
            const response = await fetch(`/api/unified-pricing/commission-profiles/${this.currentEditId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const result = await response.json();
            
            if (result.success) {
                this.showSuccess('تم حذف الملف بنجاح');
                $('#deleteModal').modal('hide');
                this.loadProfiles();
            } else {
                this.showError('فشل في حذف الملف: ' + result.message);
            }
        } catch (error) {
            console.error('Error deleting profile:', error);
            this.showError('حدث خطأ أثناء حذف الملف');
        }
    }

    filterProfiles() {
        const typeFilter = document.getElementById('profileTypeFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;
        
        this.filteredProfiles = this.profiles.filter(profile => {
            const typeMatch = !typeFilter || profile.profile_type === typeFilter;
            const statusMatch = !statusFilter || 
                (statusFilter === 'active' && profile.is_active) ||
                (statusFilter === 'inactive' && !profile.is_active);
            
            return typeMatch && statusMatch;
        });
        
        this.renderProfilesTable();
    }

    searchProfiles() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        
        if (!searchTerm) {
            this.filterProfiles();
            return;
        }
        
        this.filteredProfiles = this.profiles.filter(profile => 
            profile.display_name.toLowerCase().includes(searchTerm) ||
            (profile.description && profile.description.toLowerCase().includes(searchTerm))
        );
        
        this.renderProfilesTable();
    }

    validateForm() {
        const requiredFields = ['display_name', 'profile_type', 'admin_commission_rate', 
                               'driver_commission_rate', 'base_delivery_charge', 'per_km_delivery_rate'];
        
        let isValid = true;
        
        requiredFields.forEach(fieldName => {
            const field = document.getElementById(fieldName);
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });
        
        return isValid;
    }

    getProfileTypeLabel(type) {
        const labels = {
            'standard': 'قياسي',
            'premium': 'مميز',
            'custom': 'مخصص'
        };
        return labels[type] || type;
    }

    showLoading(show) {
        document.getElementById('loadingState').style.display = show ? 'block' : 'none';
    }

    showSuccess(message) {
        // Using jQuery toast or simple alert for now
        alert(message);
    }

    showError(message) {
        // Using jQuery toast or simple alert for now
        alert(message);
    }

    viewProfile(profileId) {
        // Navigate to restaurant management with this profile filter
        window.location.href = `/unified-pricing/restaurant-management?profile=${profileId}`;
    }
}

// Initialize when DOM is ready
$(document).ready(function() {
    window.commissionProfiles = new CommissionProfilesManagement();
});
