<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

/*
|--------------------------------------------------------------------------
| Mobile API Routes v1
|--------------------------------------------------------------------------
| نقاط النهاية المخصصة للتطبيقات المحمولة مع تنظيم حسب الإصدار
*/

Route::prefix('v1/mobile')->middleware(['mobile.cors', 'sanitize.input'])->group(function () {

    /*
    |--------------------------------------------------------------------------
    | Health Check Routes (غير محمية)
    |--------------------------------------------------------------------------
    */
    Route::get('health-check', function () {
        return response()->json([
            'success' => true,
            'message' => 'Laravel API is working',
            'timestamp' => now(),
            'version' => '1.0',
        ]);
    });

    Route::get('status', function () {
        return response()->json([
            'success' => true,
            'data' => [
                'api_version' => '1.0',
                'laravel_version' => app()->version(),
                'database' => 'connected',
                'cache' => 'working',
                'timestamp' => now(),
            ],
        ]);
    });

    /*
    |--------------------------------------------------------------------------
    | Authentication Routes (غير محمية)
    |--------------------------------------------------------------------------
    */
    Route::prefix('auth')->middleware('mobile.rate.limit:10,1')->group(function () {
        Route::post('login', [App\Http\Controllers\Api\Mobile\MobileAuthController::class, 'login']);
        Route::post('register', [App\Http\Controllers\Api\Mobile\MobileAuthController::class, 'register']);
        Route::post('forgot-password', [App\Http\Controllers\Api\Mobile\MobileAuthController::class, 'forgotPassword']);
    });

    /*
    |--------------------------------------------------------------------------
    | Protected Routes (محمية بـ Sanctum)
    |--------------------------------------------------------------------------
    */
    Route::middleware(['auth:sanctum', 'mobile.rate.limit:120,1'])->group(function () {

        // Auth routes (محمية)
        Route::prefix('auth')->group(function () {
            Route::post('logout', [App\Http\Controllers\Api\Mobile\MobileAuthController::class, 'logout']);
            Route::post('logout-all', [App\Http\Controllers\Api\Mobile\MobileAuthController::class, 'logoutAll']);
            Route::get('user', [App\Http\Controllers\Api\Mobile\MobileAuthController::class, 'user']);
            Route::post('change-password', [App\Http\Controllers\Api\Mobile\MobileAuthController::class, 'changePassword']);
            Route::post('update-fcm-token', [App\Http\Controllers\Api\Mobile\MobileAuthController::class, 'updateUserFcmToken']);
        });

        /*
        |--------------------------------------------------------------------------
        | Customer Routes
        |--------------------------------------------------------------------------
        */
        Route::prefix('customer')->middleware('mobile.user.type:customer')->group(function () {
            Route::get('profile', [App\Http\Controllers\Api\Mobile\MobileCustomerController::class, 'profile']);
            Route::put('profile', [App\Http\Controllers\Api\Mobile\MobileCustomerController::class, 'updateProfile']);
            Route::get('orders', [App\Http\Controllers\Api\Mobile\MobileCustomerController::class, 'orders']);
            Route::get('orders/{orderId}', [App\Http\Controllers\Api\Mobile\MobileCustomerController::class, 'orderDetails']);
            Route::post('orders/{orderId}/cancel', [App\Http\Controllers\Api\Mobile\MobileCustomerController::class, 'cancelOrder']);
            Route::get('restaurants', [App\Http\Controllers\Api\Mobile\MobileCustomerController::class, 'availableRestaurants']);

            // Customer Addresses
            Route::get('addresses', [App\Http\Controllers\Api\Mobile\MobileCustomerController::class, 'addresses']);
            Route::post('addresses', [App\Http\Controllers\Api\Mobile\MobileCustomerController::class, 'storeAddress']);
            Route::put('addresses/{addressId}', [App\Http\Controllers\Api\Mobile\MobileCustomerController::class, 'updateAddress']);
            Route::delete('addresses/{addressId}', [App\Http\Controllers\Api\Mobile\MobileCustomerController::class, 'deleteAddress']);

            // Customer Favorites
            Route::get('favorites', [App\Http\Controllers\Api\Mobile\MobileCustomerController::class, 'favorites']);
            Route::post('favorites', [App\Http\Controllers\Api\Mobile\MobileCustomerController::class, 'addToFavorites']);
            Route::delete('favorites', [App\Http\Controllers\Api\Mobile\MobileCustomerController::class, 'removeFromFavorites']);
        });

        /*
        |--------------------------------------------------------------------------
        | Driver Routes
        |--------------------------------------------------------------------------
        */
        Route::prefix('driver')->middleware('mobile.user.type:driver')->group(function () {
            Route::get('profile', [App\Http\Controllers\Api\Mobile\MobileDriverController::class, 'profile']);
            Route::put('profile', [App\Http\Controllers\Api\Mobile\MobileDriverController::class, 'updateProfile']);
            Route::post('location', [App\Http\Controllers\Api\Mobile\MobileDriverController::class, 'updateLocation']);
            Route::post('toggle-availability', [App\Http\Controllers\Api\Mobile\MobileDriverController::class, 'toggleAvailability']);
            Route::get('available-orders', [App\Http\Controllers\Api\Mobile\MobileDriverController::class, 'availableOrders']);
            Route::post('orders/{orderId}/accept', [App\Http\Controllers\Api\Mobile\MobileDriverController::class, 'acceptOrder']);
            Route::put('orders/{orderId}/status', [App\Http\Controllers\Api\Mobile\MobileDriverController::class, 'updateOrderStatus']);
        });

        /*
        |--------------------------------------------------------------------------
        | Vendor Routes
        |--------------------------------------------------------------------------
        */
        Route::prefix('vendor')->middleware('mobile.user.type:vendor')->group(function () {
            Route::get('profile', [App\Http\Controllers\Api\Mobile\MobileVendorController::class, 'profile']);
            Route::put('profile', [App\Http\Controllers\Api\Mobile\MobileVendorController::class, 'updateProfile']);
            Route::post('toggle-status', [App\Http\Controllers\Api\Mobile\MobileVendorController::class, 'toggleStatus']);
            Route::get('orders', [App\Http\Controllers\Api\Mobile\MobileVendorController::class, 'orders']);
            Route::put('orders/{orderId}/status', [App\Http\Controllers\Api\Mobile\MobileVendorController::class, 'updateOrderStatus']);
            Route::get('menu', [App\Http\Controllers\Api\Mobile\MobileVendorController::class, 'menu']);
            Route::put('menu/{itemId}/toggle', [App\Http\Controllers\Api\Mobile\MobileVendorController::class, 'toggleMenuItemAvailability']);
        });

        /*
        |--------------------------------------------------------------------------
        | Common Routes (للجميع)
        |--------------------------------------------------------------------------
        */
        Route::middleware('mobile.user.type:any')->group(function () {
            // يمكن إضافة routes مشتركة هنا مثل الإشعارات، الإعدادات، إلخ
        });
    });
});

/*
|--------------------------------------------------------------------------
| Zone Management API Routes - DIRECT ROUTES
|--------------------------------------------------------------------------
*/
Route::get('zones', [App\Http\Controllers\Api\ZoneController::class, 'index']);
Route::post('zones/store', [App\Http\Controllers\Api\ZoneController::class, 'store']);
Route::get('zones/published', [App\Http\Controllers\Api\ZoneController::class, 'getPublishedZones']);
Route::post('zones/delete-multiple', [App\Http\Controllers\Api\ZoneController::class, 'destroyMultiple']);
// Temporary workaround - use different route names
Route::get('zone-details/{id}', [App\Http\Controllers\Api\ZoneController::class, 'show']);
Route::put('zone-update/{id}', [App\Http\Controllers\Api\ZoneController::class, 'update']);
Route::get('zones/{id}', function($id) {
    $controller = new App\Http\Controllers\Api\ZoneController();
    return $controller->show($id);
});
Route::put('zones/{id}', [App\Http\Controllers\Api\ZoneController::class, 'update']);
Route::put('zones/{id}/publish-status', [App\Http\Controllers\Api\ZoneController::class, 'updatePublishStatus']);
Route::delete('zones/{id}', [App\Http\Controllers\Api\ZoneController::class, 'destroy']);

/*
|--------------------------------------------------------------------------
| Settings API Routes
|--------------------------------------------------------------------------
*/
Route::middleware(['web', 'auth:admin'])->prefix('settings')->group(function () {
    Route::get('global', [App\Http\Controllers\Api\SettingsController::class, 'getGlobalSettings']);
    Route::post('global', [App\Http\Controllers\Api\SettingsController::class, 'updateGlobalSettings']);
    Route::get('all', [App\Http\Controllers\Api\SettingsController::class, 'getAllSettings']);
    Route::get('test-connection', [App\Http\Controllers\Api\SettingsController::class, 'testConnection']);
    Route::get('version', [App\Http\Controllers\Api\SettingsController::class, 'getVersionSettings']);
    Route::post('version', [App\Http\Controllers\Api\SettingsController::class, 'updateVersionSettings']);
    Route::get('currency/active', [App\Http\Controllers\Api\SettingsController::class, 'getActiveCurrency']);
});

// Data Architecture Unification - User Management API Routes
// Note: Routes moved to web.php to avoid authentication issues

/*
|--------------------------------------------------------------------------
| Dashboard API Routes (Firebase Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('dashboard')->group(function () {
    Route::get('currency/active', [App\Http\Controllers\Api\DashboardController::class, 'getActiveCurrency']);
    Route::get('orders/counts', [App\Http\Controllers\Api\DashboardController::class, 'getOrderCounts']);
    Route::get('products/count', [App\Http\Controllers\Api\DashboardController::class, 'getProductCount']);
    Route::get('earnings/total', [App\Http\Controllers\Api\DashboardController::class, 'getTotalEarnings']);
    Route::get('table-count/{table}', [App\Http\Controllers\Api\DashboardController::class, 'getTableCount']);

    // المسارات المفقودة المطلوبة
    Route::get('stats', [App\Http\Controllers\Api\DashboardController::class, 'getStats']);
    Route::get('recent-orders', [App\Http\Controllers\Api\DashboardController::class, 'getRecentOrders']);
    Route::get('top-restaurants', [App\Http\Controllers\Api\DashboardController::class, 'getTopRestaurants']);
    Route::get('top-drivers', [App\Http\Controllers\Api\DashboardController::class, 'getTopDrivers']);
});

/*
|--------------------------------------------------------------------------
| OpenStreetMap API Routes
|--------------------------------------------------------------------------
*/
Route::prefix('osm')->middleware(['api'])->group(function () {
    // تحويل العنوان إلى إحداثيات
    Route::post('geocode', [App\Http\Controllers\OSMController::class, 'geocode']);

    // تحويل الإحداثيات إلى عنوان
    Route::post('reverse-geocode', [App\Http\Controllers\OSMController::class, 'reverseGeocode']);

    // حساب المسار والمسافة
    Route::post('route', [App\Http\Controllers\OSMController::class, 'getRoute']);

    // البحث عن الأماكن القريبة
    Route::post('nearby-search', [App\Http\Controllers\OSMController::class, 'nearbySearch']);

    // حساب المسافة المباشرة
    Route::post('calculate-distance', [App\Http\Controllers\OSMController::class, 'calculateDistance']);

    // البحث عن الأماكن بالاسم
    Route::post('search-places', [App\Http\Controllers\OSMController::class, 'searchPlaces']);

    // الحصول على إعدادات OSM
    Route::get('config', [App\Http\Controllers\OSMController::class, 'getConfig']);

    // اختبار الخدمات
    Route::get('test-services', [App\Http\Controllers\OSMController::class, 'testServices']);

    // المدن السعودية
    Route::get('saudi-cities', [App\Http\Controllers\OSMController::class, 'getSaudiCities']);
});

/*
|--------------------------------------------------------------------------
| OnBoarding API Routes (Firebase Replacement) - Public Access
|--------------------------------------------------------------------------
*/
Route::prefix('onboarding')->group(function () {
    Route::get('/', [App\Http\Controllers\OnBoardController::class, 'getOnBoardingItems']);
    Route::get('/{id}', [App\Http\Controllers\OnBoardController::class, 'getOnBoardingItem']);
    Route::put('/{id}', [App\Http\Controllers\OnBoardController::class, 'updateOnBoardingItem']);
});

// Public Settings Route (for mobile apps)
Route::get('settings', function() {
    return response()->json([
        'success' => true,
        'message' => 'App settings retrieved successfully',
        'data' => [
            'app_name' => 'Foodie',
            'app_version' => '8.2',
            'api_version' => 'v1',
            'currency' => 'SAR',
            'timezone' => 'UTC',
            'maintenance_mode' => false,
            'min_order_amount' => 10.00,
            'delivery_charge' => 5.00,
            'tax_rate' => 0.15,
            'languages' => ['ar', 'en'],
            'default_language' => 'ar',
        ]
    ]);
});



/*
|--------------------------------------------------------------------------
| Settings API Routes (Firebase Replacement) - Public Access
|--------------------------------------------------------------------------
*/
Route::prefix('settings')->group(function () {
    // Public settings endpoint
    Route::get('all', [App\Http\Controllers\Api\SettingsController::class, 'getAllSettings']);
    Route::get('homepage-template', [App\Http\Controllers\Api\SettingsController::class, 'getHomepageTemplate']);
    Route::post('homepage-template', [App\Http\Controllers\Api\SettingsController::class, 'setHomepageTemplate']);
    Route::get('footer-template', [App\Http\Controllers\Api\SettingsController::class, 'getFooterTemplate']);
    Route::post('footer-template', [App\Http\Controllers\Api\SettingsController::class, 'setFooterTemplate']);
    Route::get('terms-conditions', [App\Http\Controllers\Api\SettingsController::class, 'getTermsConditions']);
    Route::post('terms-conditions', [App\Http\Controllers\Api\SettingsController::class, 'setTermsConditions']);
    Route::get('placeholder-image', [App\Http\Controllers\Api\SettingsController::class, 'getPlaceholderImage']);
    Route::get('active-currency', [App\Http\Controllers\Api\SettingsController::class, 'getActiveCurrency']);
    Route::get('story-settings', [App\Http\Controllers\Api\SettingsController::class, 'getStorySettings']);
    Route::get('special-discount-offer', [App\Http\Controllers\Api\SettingsController::class, 'getSpecialDiscountOffer']);
    Route::put('special-discount-offer', [App\Http\Controllers\Api\SettingsController::class, 'updateSpecialDiscountOffer']);
    Route::post('special-discount-offer', [App\Http\Controllers\Api\SettingsController::class, 'updateSpecialDiscountOffer']); // دعم POST للنماذج
    // Admin commission routes moved to unified commission system
    Route::get('delivery-charge', [App\Http\Controllers\Api\SettingsController::class, 'getDeliveryCharge']);
    Route::put('delivery-charge', [App\Http\Controllers\Api\SettingsController::class, 'updateDeliveryCharge']);
    Route::post('delivery-charge', [App\Http\Controllers\Api\SettingsController::class, 'updateDeliveryCharge']);


    Route::get('restaurant', [App\Http\Controllers\Api\SettingsController::class, 'getRestaurantSettings']);
    Route::put('restaurant', [App\Http\Controllers\Api\SettingsController::class, 'updateRestaurantSettings']);
    Route::get('privacy-policy', [App\Http\Controllers\Api\SettingsController::class, 'getPrivacyPolicy']);
    Route::put('privacy-policy', [App\Http\Controllers\Api\SettingsController::class, 'updatePrivacyPolicy']);
    Route::post('privacy-policy', [App\Http\Controllers\Api\SettingsController::class, 'updatePrivacyPolicy']); // دعم POST للنماذج
    Route::get('email-template/{type}', [App\Http\Controllers\Api\SettingsController::class, 'getEmailTemplate']);
    Route::get('email-setting', [App\Http\Controllers\Api\SettingsController::class, 'getEmailSetting']);
    Route::get('languages', [App\Http\Controllers\Api\SettingsController::class, 'getLanguages']);
    Route::put('languages', [App\Http\Controllers\Api\SettingsController::class, 'updateLanguages']);
    Route::put('story-settings', [App\Http\Controllers\Api\SettingsController::class, 'updateStorySettings']);
    Route::post('email-templates', [App\Http\Controllers\Api\SettingsController::class, 'createEmailTemplate']);
    Route::put('email-templates/{id}', [App\Http\Controllers\Api\SettingsController::class, 'updateEmailTemplate']);
    Route::get('map-config', [App\Http\Controllers\Api\SettingsController::class, 'getMapConfig']);

    // Payment Settings API Routes (Missing routes added)
    Route::prefix('payment')->group(function () {
        Route::get('cod', [App\Http\Controllers\Api\SettingsController::class, 'getCODSettings']);
        Route::put('cod', [App\Http\Controllers\Api\SettingsController::class, 'updateCODSettings']);
        Route::get('wallet', [App\Http\Controllers\Api\SettingsController::class, 'getWalletSettings']);
        Route::put('wallet', [App\Http\Controllers\Api\SettingsController::class, 'updateWalletSettings']);
        Route::get('all', [App\Http\Controllers\Api\SettingsController::class, 'getAllPaymentSettings']);
    });

    // App Settings API Routes (Missing routes added)
    Route::prefix('app')->group(function () {
        Route::get('globals', [App\Http\Controllers\Api\SettingsController::class, 'getGlobalSettings']);
        Route::put('globals', [App\Http\Controllers\Api\SettingsController::class, 'updateGlobalSettings']);
        Route::post('globals', [App\Http\Controllers\Api\SettingsController::class, 'updateGlobalSettings']); // دعم POST للنماذج
        Route::post('globals/debug', [App\Http\Controllers\Api\SettingsController::class, 'debugGlobalSettingsValidation']);

        // Radius Configuration API Routes
        Route::get('radius', [App\Http\Controllers\Api\SettingsController::class, 'getRadiusSettings']);
        Route::put('radius', [App\Http\Controllers\Api\SettingsController::class, 'updateRadiusSettings']);
        Route::post('radius', [App\Http\Controllers\Api\SettingsController::class, 'updateRadiusSettings']); // دعم POST للنماذج

        // Stripe Payment Settings API Routes
        Route::get('stripe', [App\Http\Controllers\Api\SettingsController::class, 'getStripeSettings']);
        Route::put('stripe', [App\Http\Controllers\Api\SettingsController::class, 'updateStripeSettings']);
        Route::post('stripe', [App\Http\Controllers\Api\SettingsController::class, 'updateStripeSettings']); // دعم POST للنماذج

        // Notification Settings API Routes
        Route::get('notification', [App\Http\Controllers\Api\SettingsController::class, 'getNotificationSettings']);
        Route::put('notification', [App\Http\Controllers\Api\SettingsController::class, 'updateNotificationSettings']);
        Route::post('notification', [App\Http\Controllers\Api\SettingsController::class, 'updateNotificationSettings']); // دعم POST للنماذج
    });

    // Global settings endpoint (alternative path)
    Route::get('global', [App\Http\Controllers\Api\SettingsController::class, 'getGlobalSettings']);
    Route::put('global', [App\Http\Controllers\Api\SettingsController::class, 'updateGlobalSettings']);

    // Dine-in Settings API Routes (Book Table Settings)
    Route::get('dine-in', [App\Http\Controllers\Api\SettingsController::class, 'getDineInSettings']);
    Route::put('dine-in', [App\Http\Controllers\Api\SettingsController::class, 'updateDineInSettings']);
    Route::post('dine-in', [App\Http\Controllers\Api\SettingsController::class, 'updateDineInSettings']); // دعم POST للنماذج
});

// Removed duplicate settings routes - consolidated into main settings group above

/*
|--------------------------------------------------------------------------
| Settings API Routes (Authenticated)
|--------------------------------------------------------------------------
*/
Route::middleware(['web', 'auth:admin'])->prefix('settings')->group(function () {
    Route::get('currency/active', [App\Http\Controllers\Api\SettingsController::class, 'getActiveCurrency']);
    Route::get('map-config', function() {
        return response()->json([
            'success' => true,
            'data' => [
                'selectedMapType' => 'osm', // Force OpenStreetMap
                'defaultLat' => 24.7136,
                'defaultLng' => 46.6753,
                'defaultZoom' => 12
            ]
        ]);
    });
});



/*
|--------------------------------------------------------------------------
| Orders API Routes (Authenticated) - Additional Endpoints
|--------------------------------------------------------------------------
*/
Route::middleware(['web', 'auth:admin'])->prefix('orders')->group(function () {
    // Orders statistics endpoint
    Route::get('stats', function(\Illuminate\Http\Request $request) {
        try {
            $query = \App\Models\Order::query();

            // Apply vendor filter if provided
            if ($request->has('vendor_id') && $request->vendor_id) {
                $query->where('vendorID', $request->vendor_id);
            }

            // Apply user filter if provided
            if ($request->has('user_id') && $request->user_id) {
                $query->where('authorID', $request->user_id);
            }

            // Apply driver filter if provided
            if ($request->has('driver_id') && $request->driver_id) {
                $query->where('driverID', $request->driver_id);
            }

            $stats = [
                'total' => $query->count(),
                'today' => $query->whereDate('createdAt', today())->count(),
                'pending' => $query->where('status', 'Order Placed')->count(),
                'completed' => $query->where('status', 'Order Completed')->count(),
                'cancelled' => $query->whereIn('status', ['Order Cancelled', 'Order Rejected'])->count(),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => 'Order statistics retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving statistics: ' . $e->getMessage()
            ], 500);
        }
    });

    // Orders datatable endpoint
    Route::post('datatable', [App\Http\Controllers\Api\OrderController::class, 'datatable']);

    // Orders zones endpoint
    Route::get('zones', function() {
        try {
            $zones = \App\Models\Zone::where('isActive', true)
                        ->orderBy('name', 'asc')
                        ->select('id', 'name')
                        ->get();

            return response()->json([
                'success' => true,
                'data' => $zones,
                'message' => 'Zones retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving zones: ' . $e->getMessage()
            ], 500);
        }
    });
});

/*
|--------------------------------------------------------------------------
| Users API Routes (Authenticated - Migrated from api-no-auth)
|--------------------------------------------------------------------------
*/
Route::middleware(['web', 'auth:admin'])->prefix('users')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\UserController::class, 'index']);
    Route::post('/datatable', [App\Http\Controllers\Api\UserController::class, 'datatable']);
    Route::get('/statistics', [App\Http\Controllers\Api\UserController::class, 'getStatistics']);
    Route::get('/{id}', [App\Http\Controllers\Api\UserController::class, 'show']);
    Route::put('/{id}', [App\Http\Controllers\Api\UserController::class, 'update']);
    Route::patch('/{id}/status', [App\Http\Controllers\Api\UserController::class, 'updateUserStatus']);
});

/*
|--------------------------------------------------------------------------
| Wallet API Routes (Authenticated - Migrated from api-no-auth)
|--------------------------------------------------------------------------
*/
Route::middleware(['web', 'auth:admin'])->prefix('wallet')->group(function () {
    Route::get('/{userId}', [App\Http\Controllers\Api\WalletController::class, 'getBalance']);
    Route::post('/add', [App\Http\Controllers\Api\WalletController::class, 'addAmount']);
    Route::get('/{userId}/transactions', [App\Http\Controllers\Api\WalletController::class, 'getTransactions']);
});

/*
|--------------------------------------------------------------------------
| Dynamic Notifications API Routes (Authenticated - Migrated from api-no-auth)
|--------------------------------------------------------------------------
*/
Route::middleware(['web', 'auth:admin'])->prefix('dynamic-notifications')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\DynamicNotificationController::class, 'index']);
    Route::post('/datatable', [App\Http\Controllers\Api\DynamicNotificationController::class, 'datatable']);
    Route::post('/', [App\Http\Controllers\Api\DynamicNotificationController::class, 'store']);
    Route::get('/{id}', [App\Http\Controllers\Api\DynamicNotificationController::class, 'show']);
    Route::put('/{id}', [App\Http\Controllers\Api\DynamicNotificationController::class, 'update']);
    Route::delete('/{id}', [App\Http\Controllers\Api\DynamicNotificationController::class, 'destroy']);
    Route::post('/bulk-delete', [App\Http\Controllers\Api\DynamicNotificationController::class, 'bulkDelete']);
    Route::get('/stats/overview', [App\Http\Controllers\Api\DynamicNotificationController::class, 'stats']);
});

/*
|--------------------------------------------------------------------------
| Email Templates API Routes (Authenticated - Migrated from api-no-auth)
|--------------------------------------------------------------------------
*/
Route::middleware(['web', 'auth:admin'])->prefix('email-templates')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\EmailTemplateController::class, 'index']);
    Route::post('/datatable', [App\Http\Controllers\Api\EmailTemplateController::class, 'datatable']);
    Route::post('/', [App\Http\Controllers\Api\EmailTemplateController::class, 'store']);
    Route::get('/{id}', [App\Http\Controllers\Api\EmailTemplateController::class, 'show']);
    Route::put('/{id}', [App\Http\Controllers\Api\EmailTemplateController::class, 'update']);
    Route::delete('/{id}', [App\Http\Controllers\Api\EmailTemplateController::class, 'destroy']);
    Route::post('/bulk-delete', [App\Http\Controllers\Api\EmailTemplateController::class, 'bulkDelete']);
    Route::get('/type/{type}', [App\Http\Controllers\Api\EmailTemplateController::class, 'getByType']);
});

/*
|--------------------------------------------------------------------------
| Document Management API Routes (Real Implementation)
|--------------------------------------------------------------------------
*/
Route::prefix('documents')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\DocumentController::class, 'index']);
    Route::post('/', [App\Http\Controllers\Api\DocumentController::class, 'store']);
    Route::get('/{id}', [App\Http\Controllers\Api\DocumentController::class, 'show']);
    Route::put('/{id}', [App\Http\Controllers\Api\DocumentController::class, 'update']);
    Route::delete('/{id}', [App\Http\Controllers\Api\DocumentController::class, 'destroy']);
    Route::get('/verifications/{userId}', [App\Http\Controllers\Api\DocumentController::class, 'getVerifications']);

    // Legacy mock routes for backward compatibility
    Route::get('/mock', function() {
        $page = request('page', 1);
        $perPage = request('per_page', 10);
        $search = request('search', '');
        $sortBy = request('sort_by', 'title');
        $sortDirection = request('sort_direction', 'asc');

        // Mock document data
        $allDocuments = [
            [
                'id' => 'doc1',
                'title' => 'بطاقة الهوية',
                'type' => 'restaurant',
                'front_side' => true,
                'back_side' => true,
                'enabled' => true
            ],
            [
                'id' => 'doc2',
                'title' => 'رخصة المطعم',
                'type' => 'restaurant',
                'front_side' => true,
                'back_side' => false,
                'enabled' => true
            ],
            [
                'id' => 'doc3',
                'title' => 'شهادة السلامة الغذائية',
                'type' => 'restaurant',
                'front_side' => true,
                'back_side' => true,
                'enabled' => false
            ],
            [
                'id' => 'doc4',
                'title' => 'رخصة القيادة',
                'type' => 'driver',
                'front_side' => true,
                'back_side' => true,
                'enabled' => true
            ],
            [
                'id' => 'doc5',
                'title' => 'شهادة التأمين',
                'type' => 'driver',
                'front_side' => true,
                'back_side' => false,
                'enabled' => true
            ]
        ];

        // Filter by search
        if ($search) {
            $allDocuments = array_filter($allDocuments, function($doc) use ($search) {
                return stripos($doc['title'], $search) !== false ||
                       stripos($doc['type'], $search) !== false;
            });
        }

        // Sort
        usort($allDocuments, function($a, $b) use ($sortBy, $sortDirection) {
            $aValue = $a[$sortBy] ?? '';
            $bValue = $b[$sortBy] ?? '';

            if ($sortDirection === 'desc') {
                return strcmp($bValue, $aValue);
            }
            return strcmp($aValue, $bValue);
        });

        $total = count($allDocuments);
        $offset = ($page - 1) * $perPage;
        $documents = array_slice($allDocuments, $offset, $perPage);

        return response()->json([
            'success' => true,
            'data' => [
                'data' => $documents,
                'total' => $total,
                'per_page' => $perPage,
                'current_page' => $page
            ]
        ]);
    });

    Route::put('{id}/status', function($id) {
        // Mock status update
        return response()->json([
            'success' => true,
            'message' => 'Document status updated successfully'
        ]);
    });

    Route::delete('{id}', function($id) {
        // Mock delete
        return response()->json([
            'success' => true,
            'message' => 'Document deleted successfully'
        ]);
    });

    Route::post('bulk-delete', function() {
        // Mock bulk delete
        return response()->json([
            'success' => true,
            'message' => 'Documents deleted successfully'
        ]);
    });

    Route::get('count', function() {
        // Mock count check
        return response()->json([
            'success' => true,
            'data' => ['count' => 3]
        ]);
    });

    Route::post('verify-users', function() {
        // Mock user verification
        return response()->json([
            'success' => true,
            'message' => 'Users verified successfully'
        ]);
    });

    Route::get('{id}', function($id) {
        // Mock single document data
        $documents = [
            'doc1' => [
                'id' => 'doc1',
                'title' => 'بطاقة الهوية',
                'type' => 'restaurant',
                'front_side' => true,
                'back_side' => true,
                'enabled' => true
            ],
            'doc2' => [
                'id' => 'doc2',
                'title' => 'رخصة المطعم',
                'type' => 'restaurant',
                'front_side' => true,
                'back_side' => false,
                'enabled' => true
            ],
            'doc3' => [
                'id' => 'doc3',
                'title' => 'شهادة السلامة الغذائية',
                'type' => 'restaurant',
                'front_side' => true,
                'back_side' => true,
                'enabled' => false
            ],
            'doc4' => [
                'id' => 'doc4',
                'title' => 'رخصة القيادة',
                'type' => 'driver',
                'front_side' => true,
                'back_side' => true,
                'enabled' => true
            ],
            'doc5' => [
                'id' => 'doc5',
                'title' => 'شهادة التأمين',
                'type' => 'driver',
                'front_side' => true,
                'back_side' => false,
                'enabled' => true
            ]
        ];

        $document = $documents[$id] ?? null;

        if (!$document) {
            return response()->json([
                'success' => false,
                'message' => 'Document not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $document
        ]);
    });

    Route::put('{id}', function($id) {
        // Mock document update
        return response()->json([
            'success' => true,
            'message' => 'Document updated successfully'
        ]);
    });

    Route::get('check-enabled', function() {
        // Mock enabled documents check
        $type = request('type', 'restaurant');
        $excludeId = request('exclude_id');

        // Mock count based on type
        $count = $type === 'restaurant' ? 2 : 1;

        return response()->json([
            'success' => true,
            'data' => ['count' => $count]
        ]);
    });

    Route::post('update-verification-status', function() {
        // Mock verification status update
        return response()->json([
            'success' => true,
            'message' => 'Verification status updated successfully'
        ]);
    });
});

Route::prefix('document-verification')->group(function () {
    Route::get('{vendorId}', function($vendorId) {
        // Mock verification data
        $verifications = [
            'documents' => [
                [
                    'document_id' => 'doc1',
                    'status' => 'approved',
                    'front_image' => 'https://via.placeholder.com/300x200/28a745/ffffff?text=ID+Front',
                    'back_image' => 'https://via.placeholder.com/300x200/28a745/ffffff?text=ID+Back'
                ],
                [
                    'document_id' => 'doc2',
                    'status' => 'uploaded',
                    'front_image' => 'https://via.placeholder.com/300x200/007bff/ffffff?text=License+Front',
                    'back_image' => null
                ],
                [
                    'document_id' => 'doc3',
                    'status' => 'pending',
                    'front_image' => null,
                    'back_image' => null
                ]
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => $verifications
        ]);
    });

    Route::post('status', function() {
        // Mock status update
        return response()->json([
            'success' => true,
            'message' => 'Document status updated successfully'
        ]);
    });
});

Route::prefix('notifications')->group(function () {
    Route::post('send', function() {
        // Mock notification sending
        return response()->json([
            'success' => true,
            'message' => 'Notification sent successfully'
        ]);
    });
});



/*
|--------------------------------------------------------------------------
| Restaurants/Vendors API Routes (Firebase Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('restaurants')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\RestaurantController::class, 'index']);
    Route::post('/', [App\Http\Controllers\Api\RestaurantController::class, 'store']);
    Route::post('/datatable', [App\Http\Controllers\Api\RestaurantController::class, 'getDataTableData']);
    Route::get('/{id}', [App\Http\Controllers\Api\RestaurantController::class, 'show']);
    Route::put('/{id}', [App\Http\Controllers\Api\RestaurantController::class, 'update']);
    Route::delete('/{id}', [App\Http\Controllers\Api\RestaurantController::class, 'destroy']);
    Route::get('/{id}/orders-count', [App\Http\Controllers\Api\RestaurantController::class, 'getOrdersCount']);
    Route::get('/{id}/status', [App\Http\Controllers\Api\RestaurantController::class, 'getVendorStatus']);
    Route::put('/{id}/working-hours', [App\Http\Controllers\Api\RestaurantController::class, 'updateWorkingHours']);
    Route::put('/{id}/special-discount', [App\Http\Controllers\Api\RestaurantController::class, 'updateSpecialDiscount']);
});

/*
|--------------------------------------------------------------------------
| Cuisines API Routes (أنواع المطابخ)
|--------------------------------------------------------------------------
*/
Route::prefix('cuisines')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\CuisineController::class, 'index']);
    Route::get('/active', [App\Http\Controllers\Api\CuisineController::class, 'getActive']);
    Route::post('/', [App\Http\Controllers\Api\CuisineController::class, 'store']);
    Route::get('/{id}', [App\Http\Controllers\Api\CuisineController::class, 'show']);
    Route::put('/{id}', [App\Http\Controllers\Api\CuisineController::class, 'update']);
    Route::delete('/{id}', [App\Http\Controllers\Api\CuisineController::class, 'destroy']);
    Route::patch('/{id}/toggle-status', [App\Http\Controllers\Api\CuisineController::class, 'toggleStatus']);
});

Route::prefix('vendors')->group(function () {
    Route::get('/data', [App\Http\Controllers\VendorController::class, 'getVendorsData']); // Add specific data route
    Route::get('/', [App\Http\Controllers\Api\VendorController::class, 'getUsersByRole'])->defaults('role', 'vendor');
    Route::get('/pending', [App\Http\Controllers\Api\VendorController::class, 'getPendingVendors']);
    Route::get('/approved', [App\Http\Controllers\Api\VendorController::class, 'getApprovedVendors']);
    // Bulk commission update moved to unified commission system
    Route::get('/{id}', [App\Http\Controllers\Api\VendorController::class, 'show']);
    Route::put('/{id}', [App\Http\Controllers\Api\VendorController::class, 'update']);
    Route::put('/{id}/status', [App\Http\Controllers\Api\VendorController::class, 'updateUserStatus']);
    Route::put('/{id}/author', [App\Http\Controllers\Api\VendorController::class, 'updateAuthor']);
    Route::put('/{id}/subscription', [App\Http\Controllers\Api\VendorController::class, 'updateSubscription']);
    Route::get('/{id}/delivery-charges', [App\Http\Controllers\Api\VendorController::class, 'getDeliveryCharges']);
    Route::delete('/{id}', [App\Http\Controllers\Api\VendorController::class, 'destroy']);
});

/*
|--------------------------------------------------------------------------
| Gift Cards API Routes (Firebase Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('gift-cards')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\GiftCardController::class, 'index']);
    Route::post('/', [App\Http\Controllers\Api\GiftCardController::class, 'store']);
    Route::get('/{id}', [App\Http\Controllers\Api\GiftCardController::class, 'show']);
    Route::put('/{id}', [App\Http\Controllers\Api\GiftCardController::class, 'update']);
    Route::delete('/{id}', [App\Http\Controllers\Api\GiftCardController::class, 'destroy']);
});

/*
|--------------------------------------------------------------------------
| Upload API Routes (Firebase Storage Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('upload')->group(function () {
    Route::post('gift-card-image', [App\Http\Controllers\Api\UploadController::class, 'uploadGiftCardImage']);
    Route::post('gift-card-image-base64', [App\Http\Controllers\Api\UploadController::class, 'uploadGiftCardImageBase64']);
    Route::delete('delete-image', [App\Http\Controllers\Api\UploadController::class, 'deleteImage']);
});

/*
|--------------------------------------------------------------------------
| Categories API Routes (Firebase Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('categories')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\CategoryController::class, 'index']);
    Route::post('/datatable', [App\Http\Controllers\Api\CategoryController::class, 'getDataTableData']);
    Route::get('/vendor-categories', [App\Http\Controllers\Api\CategoryController::class, 'getVendorCategories']);
    Route::post('/', [App\Http\Controllers\Api\CategoryController::class, 'store']);
    Route::get('/{id}', [App\Http\Controllers\Api\CategoryController::class, 'show']);
    Route::put('/{id}', [App\Http\Controllers\Api\CategoryController::class, 'update']);
    Route::put('/{id}/publish', [App\Http\Controllers\Api\CategoryController::class, 'updatePublishStatus']);
    Route::delete('/{id}', [App\Http\Controllers\Api\CategoryController::class, 'destroy']);
    Route::get('/{id}/products-count', [App\Http\Controllers\Api\CategoryController::class, 'getProductsCount']);
});

/*
|--------------------------------------------------------------------------
| Subscription Plans API Routes (Firebase Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('subscription-plans')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\SubscriptionPlanController::class, 'index']);
    Route::post('/', [App\Http\Controllers\Api\SubscriptionPlanController::class, 'store']);
    Route::get('/active', [App\Http\Controllers\Api\SubscriptionPlanController::class, 'getActivePlans']);
    Route::get('/{id}', [App\Http\Controllers\Api\SubscriptionPlanController::class, 'show']);
    Route::put('/{id}', [App\Http\Controllers\Api\SubscriptionPlanController::class, 'update']);
    Route::delete('/{id}', [App\Http\Controllers\Api\SubscriptionPlanController::class, 'destroy']);
    Route::get('/{id}/vendors', [App\Http\Controllers\Api\SubscriptionPlanController::class, 'getVendorsByPlan']);
});

/*
|--------------------------------------------------------------------------
| Currencies API Routes (Firebase Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('currencies')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\CurrencyController::class, 'index']);
    Route::get('/active', [App\Http\Controllers\Api\CurrencyController::class, 'getActiveCurrency']);
    Route::get('/{id}', [App\Http\Controllers\Api\CurrencyController::class, 'show']);

    // Protected routes for admin operations
    Route::middleware(['web', 'auth:admin'])->group(function () {
        Route::post('/', [App\Http\Controllers\Api\CurrencyController::class, 'store']);
        Route::put('/{id}', [App\Http\Controllers\Api\CurrencyController::class, 'update']);
        Route::delete('/{id}', [App\Http\Controllers\Api\CurrencyController::class, 'destroy']);
        Route::put('/deactivate-others', [App\Http\Controllers\Api\CurrencyController::class, 'deactivateOthers']);
        Route::post('/bulk-delete', [App\Http\Controllers\Api\CurrencyController::class, 'bulkDelete']);
    });
});

/*
|--------------------------------------------------------------------------
| Hierarchical Settings Management API Routes
|--------------------------------------------------------------------------
*/
Route::prefix('hierarchical-settings')->group(function () {
    // Handle OPTIONS requests for CORS
    Route::options('/{vendorId}', function () {
        return response()->json(['status' => 'OK'], 200)
            ->header('Access-Control-Allow-Origin', '*')
            ->header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
            ->header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    });

    Route::get('/{vendorId}', [App\Http\Controllers\Api\HierarchicalSettingsController::class, 'getVendorHierarchicalSettings']);
    Route::put('/{vendorId}', [App\Http\Controllers\Api\HierarchicalSettingsController::class, 'updateVendorHierarchicalSettings']);
});

/*
|--------------------------------------------------------------------------
| Vendor Features Management API Routes (Updated for Unified System)
|--------------------------------------------------------------------------
*/
Route::middleware(['web'])->prefix('vendor-features')->group(function () {
    // Delivery charge and dine-in routes (non-commission features)
    Route::get('/dine-in-list', [App\Http\Controllers\Api\VendorDineInController::class, 'getVendorsWithDineInStatus']);
    Route::post('/bulk-delivery-charge', [App\Http\Controllers\Api\VendorDeliveryChargeController::class, 'bulkUpdateDeliveryCharges']);
    Route::post('/bulk-dine-in', [App\Http\Controllers\Api\VendorDineInController::class, 'bulkUpdateDineInSettings']);
    Route::post('/calculate-delivery-charge', [App\Http\Controllers\Api\VendorDeliveryChargeController::class, 'calculateDeliveryCharge']);

    // Individual vendor routes (non-commission)
    Route::get('/{vendorId}/delivery-charge', [App\Http\Controllers\Api\VendorDeliveryChargeController::class, 'getVendorDeliveryChargeSettings']);
    Route::put('/{vendorId}/delivery-charge', [App\Http\Controllers\Api\VendorDeliveryChargeController::class, 'updateVendorDeliveryChargeSettings']);
    Route::get('/{vendorId}/dine-in', [App\Http\Controllers\Api\VendorDineInController::class, 'getVendorDineInSettings']);
    Route::put('/{vendorId}/dine-in', [App\Http\Controllers\Api\VendorDineInController::class, 'updateVendorDineInSettings']);

    // Commission routes moved to unified-commission prefix
});

/*
|--------------------------------------------------------------------------
| Enhanced Commission System API Routes - REMOVED
| All functionality moved to Unified Commission System
|--------------------------------------------------------------------------
*/

/*
|--------------------------------------------------------------------------
| Unified Pricing System API Routes (Integrated with Commission Management)
|--------------------------------------------------------------------------
*/
Route::middleware(['web'])->prefix('unified-pricing')->group(function () {
    // Settings management
    Route::get('/settings', [App\Http\Controllers\Api\UnifiedPricingController::class, 'index']);
    Route::post('/settings', [App\Http\Controllers\Api\UnifiedPricingController::class, 'store']);
    Route::get('/settings/{id}', [App\Http\Controllers\Api\UnifiedPricingController::class, 'show']);
    Route::put('/settings/{id}', [App\Http\Controllers\Api\UnifiedPricingController::class, 'update']);
    Route::delete('/settings/{id}', [App\Http\Controllers\Api\UnifiedPricingController::class, 'destroy']);

    // Pricing calculations
    Route::post('/calculate', [App\Http\Controllers\Api\UnifiedPricingController::class, 'calculatePricing']);
    Route::post('/calculate-test', [App\Http\Controllers\Api\UnifiedPricingController::class, 'calculateTestPricing']);

    // Restaurant-specific pricing management (NEW - Commission Integration)
    Route::get('/restaurants', [App\Http\Controllers\Api\UnifiedPricingController::class, 'getRestaurantsWithProfiles']);
    Route::post('/restaurants/profiles', [App\Http\Controllers\Api\UnifiedPricingController::class, 'createRestaurantProfile']);
    Route::get('/restaurants/{restaurantId}/profile', [App\Http\Controllers\Api\UnifiedPricingController::class, 'getRestaurantProfile']);
    Route::put('/restaurants/profiles/{profileId}', [App\Http\Controllers\Api\UnifiedPricingController::class, 'updateRestaurantProfile']);
    Route::post('/restaurants/calculate', [App\Http\Controllers\Api\UnifiedPricingController::class, 'calculateRestaurantPricing']);

    // Bulk operations and auto-assignment
    Route::post('/restaurants/auto-assign', [App\Http\Controllers\Api\UnifiedPricingController::class, 'autoAssignProfiles']);
    Route::post('/restaurants/assign-profile', [App\Http\Controllers\Api\UnifiedPricingController::class, 'assignProfileToRestaurant']);
    Route::post('/restaurants/bulk-operations', [App\Http\Controllers\Api\UnifiedPricingController::class, 'bulkRestaurantOperations']);
    Route::get('/restaurants/{restaurantId}/metrics', [App\Http\Controllers\Api\UnifiedPricingController::class, 'getRestaurantMetrics']);

    // Commission profiles for bulk operations
    Route::get('/commission-profiles', [App\Http\Controllers\Api\UnifiedPricingController::class, 'getCommissionProfiles']);
    Route::post('/commission-profiles', [App\Http\Controllers\Api\UnifiedPricingController::class, 'createCommissionProfile']);
    Route::put('/commission-profiles/{id}', [App\Http\Controllers\Api\UnifiedPricingController::class, 'updateCommissionProfile']);
    Route::delete('/commission-profiles/{id}', [App\Http\Controllers\Api\UnifiedPricingController::class, 'deleteCommissionProfile']);

    // Analytics and reporting
    Route::get('/analytics', [App\Http\Controllers\Api\UnifiedPricingController::class, 'getAnalytics']);
    Route::get('/applicable-setting', [App\Http\Controllers\Api\UnifiedPricingController::class, 'getApplicableSetting']);
});

/*
|--------------------------------------------------------------------------
| Legacy Commission System API Routes - REMOVED
| All functionality moved to Unified Commission System (/api/unified-commission/*)
|--------------------------------------------------------------------------
*/

/*
|--------------------------------------------------------------------------
| Vendor Categories API Routes (Firebase Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('vendor-categories')->group(function () {
    Route::get('/published', [App\Http\Controllers\Api\CategoryController::class, 'getVendorCategories']);
});

/*
|--------------------------------------------------------------------------
| Stories API Routes (Firebase Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('stories')->group(function () {
    Route::get('/vendor/{vendorId}', [App\Http\Controllers\Api\StoryController::class, 'getVendorStory']);
    Route::post('/{vendorId}', [App\Http\Controllers\Api\StoryController::class, 'store']);
    Route::put('/{vendorId}/thumbnail', [App\Http\Controllers\Api\StoryController::class, 'updateThumbnail']);
    Route::delete('/{vendorId}', [App\Http\Controllers\Api\StoryController::class, 'destroy']);
});

/*
|--------------------------------------------------------------------------
| Subscription History API Routes (Firebase Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('subscription-history')->group(function () {
    Route::post('/', [App\Http\Controllers\Api\SubscriptionHistoryController::class, 'store']);
    Route::get('/user/{userId}/latest', [App\Http\Controllers\Api\SubscriptionHistoryController::class, 'getLatestByUser']);
    Route::put('/{id}', [App\Http\Controllers\Api\SubscriptionHistoryController::class, 'update']);
});

/*
|--------------------------------------------------------------------------
| Users API Routes (Enhanced - Admin Panel Access)
|--------------------------------------------------------------------------
| Note: These endpoints are for the admin panel which is already protected
| by the web routes. We use 'web' middleware to maintain session state.
*/
Route::middleware(['web'])->prefix('users')->group(function () {
    // Specific routes first (before dynamic {id} routes)
    Route::get('/', [App\Http\Controllers\Api\UserApiController::class, 'getUsers']);
    Route::post('/datatable', [App\Http\Controllers\Api\UserApiController::class, 'getUsersDataTable']);
    Route::get('/statistics', [App\Http\Controllers\Api\UserApiController::class, 'getUserStatistics']);

    // Dynamic routes last
    Route::get('/{id}', [App\Http\Controllers\Api\UserApiController::class, 'getUser']);
    Route::match(['PUT', 'PATCH'], '/{id}', [App\Http\Controllers\Api\UserApiController::class, 'updateUser']);
    Route::match(['PUT', 'PATCH'], '/{id}/status', [App\Http\Controllers\Api\UserApiController::class, 'updateUserStatus']);
    Route::delete('/{id}', [App\Http\Controllers\Api\UserApiController::class, 'deleteUser']);
});

/*
|--------------------------------------------------------------------------
| Users API Routes (Legacy Firebase Replacement - Deprecated)
|--------------------------------------------------------------------------
*/
Route::prefix('users-legacy')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\UserController::class, 'index']);
    Route::post('/datatable', [App\Http\Controllers\Api\UserController::class, 'datatable']);
    Route::post('/', [App\Http\Controllers\Api\UserController::class, 'store']);
    Route::get('/{id}', [App\Http\Controllers\Api\UserController::class, 'show']);
    Route::put('/{id}', [App\Http\Controllers\Api\UserController::class, 'update']);
    Route::put('/{id}/subscription', [App\Http\Controllers\Api\UserController::class, 'updateSubscription']);
    Route::delete('/{id}', [App\Http\Controllers\Api\UserController::class, 'destroy']);
    Route::get('/{id}/status', [App\Http\Controllers\Api\UserController::class, 'getUserStatus']);
    Route::patch('/{id}/status', [App\Http\Controllers\Api\UserController::class, 'updateUserStatus']);
    Route::put('/{id}/verification', [App\Http\Controllers\Api\UserController::class, 'updateVerification']);
    Route::get('/by-subscription/{planId}', [App\Http\Controllers\Api\UserController::class, 'getUsersBySubscription']);
    Route::get('/by-role/{role}', [App\Http\Controllers\Api\UserController::class, 'getUsersByRole']);
});

/*
|--------------------------------------------------------------------------
| Wallet API Routes (Firebase Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('wallet')->group(function () {
    Route::get('/{userId}', [App\Http\Controllers\Api\WalletController::class, 'getBalance']);
    Route::post('/add', [App\Http\Controllers\Api\WalletController::class, 'addAmount']);
    Route::get('/{userId}/transactions', [App\Http\Controllers\Api\WalletController::class, 'getTransactions']);
});

/*
|--------------------------------------------------------------------------
| File Upload API Routes (Firebase Storage Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('upload')->group(function () {
    Route::post('image', [App\Http\Controllers\Api\FileUploadController::class, 'uploadImage']);
    Route::post('gallery-image', [App\Http\Controllers\Api\FileUploadController::class, 'uploadGalleryImage']);
    Route::post('menu-image', [App\Http\Controllers\Api\FileUploadController::class, 'uploadMenuImage']);
    Route::post('story-thumbnail', [App\Http\Controllers\Api\FileUploadController::class, 'uploadStoryThumbnail']);
    Route::post('story-video', [App\Http\Controllers\Api\FileUploadController::class, 'uploadStoryVideo']);
    Route::post('owner-image', [App\Http\Controllers\Api\FileUploadController::class, 'uploadOwnerImage']);
    Route::post('language-flag', [App\Http\Controllers\Api\FileUploadController::class, 'uploadLanguageFlag']);
});

Route::prefix('files')->group(function () {
    Route::delete('delete', [App\Http\Controllers\Api\FileUploadController::class, 'deleteFile']);
});



// Removed duplicate dynamic-notifications routes - using authenticated version above

// Removed duplicate email-templates routes - using authenticated version above

/*
|--------------------------------------------------------------------------
| Products API Routes (Firebase Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('products')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\ProductController::class, 'index']);
    Route::post('/', [App\Http\Controllers\Api\ProductController::class, 'store']);
    Route::get('/{id}', [App\Http\Controllers\Api\ProductController::class, 'show']);
    Route::put('/{id}', [App\Http\Controllers\Api\ProductController::class, 'update']);
    Route::put('/{id}/status', [App\Http\Controllers\Api\ProductController::class, 'updateStatus']);
    Route::delete('/{id}', [App\Http\Controllers\Api\ProductController::class, 'destroy']);
    Route::post('/bulk-delete', [App\Http\Controllers\Api\ProductController::class, 'destroyMultiple']);
});

// Coupons API Routes
Route::prefix('coupons')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\CouponController::class, 'index']);
    Route::post('/', [App\Http\Controllers\Api\CouponController::class, 'store']);
    Route::get('/{id}', [App\Http\Controllers\Api\CouponController::class, 'show']);
    Route::put('/{id}', [App\Http\Controllers\Api\CouponController::class, 'update']);
    Route::delete('/{id}', [App\Http\Controllers\Api\CouponController::class, 'destroy']);
});

/*
|--------------------------------------------------------------------------
| Orders API Routes (Firebase Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('orders')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\OrderController::class, 'index']);
    Route::post('/datatable', [App\Http\Controllers\Api\OrderController::class, 'datatable']);
    Route::get('/stats', [App\Http\Controllers\Api\OrderController::class, 'stats']);
    Route::get('/count', [App\Http\Controllers\Api\OrderController::class, 'getCount']);
    Route::get('/zones', [App\Http\Controllers\Api\OrderController::class, 'getZones']);
    Route::post('/bulk-delete', [App\Http\Controllers\Api\OrderController::class, 'bulkDelete']);
    Route::get('/vendor/{vendorId}', [App\Http\Controllers\Api\OrderController::class, 'getVendorInfo']);
    Route::get('/{id}', [App\Http\Controllers\Api\OrderController::class, 'show']);
    Route::get('/{id}/reviews', [App\Http\Controllers\Api\OrderController::class, 'getOrderReviews']);
    Route::get('/{id}/print', [App\Http\Controllers\Api\OrderController::class, 'print']);
    Route::put('/{id}', [App\Http\Controllers\Api\OrderController::class, 'update']);
    Route::put('/{id}/status', [App\Http\Controllers\Api\OrderController::class, 'updateStatus']);
    Route::delete('/{id}', [App\Http\Controllers\Api\OrderController::class, 'destroy']);
});

/*
|--------------------------------------------------------------------------
| Authentication API Routes
|--------------------------------------------------------------------------
*/
Route::prefix('auth')->group(function () {
    Route::post('password-reset', [App\Http\Controllers\Api\AuthController::class, 'sendPasswordReset']);
    Route::delete('user/{id}', [App\Http\Controllers\Api\AuthController::class, 'deleteUser']);
});

/*
|--------------------------------------------------------------------------
| Transactions API Routes (Firebase Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('transactions')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\TransactionController::class, 'index']);
    Route::post('/datatable', [App\Http\Controllers\Api\TransactionController::class, 'datatable']);
    Route::get('/stats', [App\Http\Controllers\Api\TransactionController::class, 'stats']);
    Route::get('/{id}', [App\Http\Controllers\Api\TransactionController::class, 'show']);
    Route::post('/', [App\Http\Controllers\Api\TransactionController::class, 'store']);
    Route::put('/{id}', [App\Http\Controllers\Api\TransactionController::class, 'update']);
});

/*
|--------------------------------------------------------------------------
| Order Transactions API Routes (Firebase Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('order-transactions')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\TransactionController::class, 'getOrderTransactions']);
    Route::post('/datatable', [App\Http\Controllers\Api\TransactionController::class, 'orderTransactionsDatatable']);
    Route::get('/stats', [App\Http\Controllers\Api\TransactionController::class, 'getOrderTransactionsStats']);
});

/*
|--------------------------------------------------------------------------
| Payments API Routes (Firebase Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('payments')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\TransactionController::class, 'getPayments']);
    Route::post('/datatable', [App\Http\Controllers\Api\TransactionController::class, 'paymentsDatatable']);
    Route::get('/stats', [App\Http\Controllers\Api\TransactionController::class, 'getPaymentsStats']);
    Route::get('/vendors', [App\Http\Controllers\Api\TransactionController::class, 'getVendorPayments']);
    Route::post('/process', [App\Http\Controllers\Api\TransactionController::class, 'processPayment']);
    Route::put('/{id}/status', [App\Http\Controllers\Api\TransactionController::class, 'updatePaymentStatus']);
});

/*
|--------------------------------------------------------------------------
| Notifications API Routes (Firebase Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('notifications')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\NotificationController::class, 'index']);
    Route::post('/datatable', [App\Http\Controllers\Api\NotificationController::class, 'datatable']);
    Route::get('/{id}', [App\Http\Controllers\Api\NotificationController::class, 'show']);
    Route::post('/', [App\Http\Controllers\Api\NotificationController::class, 'store']);
    Route::put('/{id}', [App\Http\Controllers\Api\NotificationController::class, 'update']);
    Route::delete('/{id}', [App\Http\Controllers\Api\NotificationController::class, 'destroy']);
    Route::post('/{id}/send', [App\Http\Controllers\Api\NotificationController::class, 'send']);
});

/*
|--------------------------------------------------------------------------
| Reports API Routes (Firebase Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('reports')->group(function () {
    Route::get('/sales', [App\Http\Controllers\Api\ReportController::class, 'salesReport']);
    Route::get('/restaurant-performance', [App\Http\Controllers\Api\ReportController::class, 'restaurantPerformance']);
    Route::get('/driver-performance', [App\Http\Controllers\Api\ReportController::class, 'driverPerformance']);
    Route::get('/financial-summary', [App\Http\Controllers\Api\ReportController::class, 'financialSummary']);
});

/*
|--------------------------------------------------------------------------
| Languages API Routes (Firebase Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('languages')->group(function () {
    // Public route for getting languages (for header, etc.)
    Route::get('/', [App\Http\Controllers\Api\LanguageController::class, 'index']);

    // Protected routes for admin operations
    Route::middleware(['web', 'auth:admin'])->group(function () {
        Route::post('/', [App\Http\Controllers\Api\LanguageController::class, 'store']);
        Route::get('/{id}', [App\Http\Controllers\Api\LanguageController::class, 'show']);
        Route::put('/{id}', [App\Http\Controllers\Api\LanguageController::class, 'update']);
        Route::delete('/{id}', [App\Http\Controllers\Api\LanguageController::class, 'destroy']);
    });
});

/*
|--------------------------------------------------------------------------
| CMS API Routes (Firebase Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('cms')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\CmsController::class, 'index']);
    Route::post('/datatable', [App\Http\Controllers\Api\CmsController::class, 'datatable']);
    Route::get('/slug/{slug}', [App\Http\Controllers\Api\CmsController::class, 'getBySlug']);
    Route::get('/{id}', [App\Http\Controllers\Api\CmsController::class, 'show']);
    Route::post('/', [App\Http\Controllers\Api\CmsController::class, 'store']);
    Route::put('/{id}', [App\Http\Controllers\Api\CmsController::class, 'update']);
    Route::delete('/{id}', [App\Http\Controllers\Api\CmsController::class, 'destroy']);
});

/*
|--------------------------------------------------------------------------
| Banners API Routes (Firebase Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('banners')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\BannerController::class, 'index']);
    Route::post('/datatable', [App\Http\Controllers\Api\BannerController::class, 'datatable']);
    Route::get('/published', [App\Http\Controllers\Api\BannerController::class, 'getPublished']);
    Route::get('/{id}', [App\Http\Controllers\Api\BannerController::class, 'show']);
    Route::post('/', [App\Http\Controllers\Api\BannerController::class, 'store']);
    Route::put('/{id}', [App\Http\Controllers\Api\BannerController::class, 'update']);
    Route::delete('/{id}', [App\Http\Controllers\Api\BannerController::class, 'destroy']);
    Route::post('/bulk-delete', [App\Http\Controllers\Api\BannerController::class, 'bulkDelete']);
    Route::put('/{id}/publish-status', [App\Http\Controllers\Api\BannerController::class, 'updatePublishStatus']);
});

/*
|--------------------------------------------------------------------------
| Customers API Routes (Firebase Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('customers')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\CustomerController::class, 'index']);
    Route::post('/datatable', [App\Http\Controllers\Api\CustomerController::class, 'datatable']);
    Route::get('/stats', [App\Http\Controllers\Api\CustomerController::class, 'stats']);
    Route::get('/{id}', [App\Http\Controllers\Api\CustomerController::class, 'show']);
    Route::post('/', [App\Http\Controllers\Api\CustomerController::class, 'store']);
    Route::put('/{id}', [App\Http\Controllers\Api\CustomerController::class, 'update']);
    Route::delete('/{id}', [App\Http\Controllers\Api\CustomerController::class, 'destroy']);
});

/*
|--------------------------------------------------------------------------
| Drivers API Routes (Firebase Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('drivers')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\DriverFixedController::class, 'index']);
    Route::get('/available', [App\Http\Controllers\Api\DriverController::class, 'getAvailableDrivers']);
    Route::post('/datatable', [App\Http\Controllers\Api\DriverController::class, 'datatable']);
    Route::get('/stats', [App\Http\Controllers\Api\DriverFixedController::class, 'stats']);
    Route::get('/{id}', [App\Http\Controllers\Api\DriverController::class, 'show']);
    Route::post('/', [App\Http\Controllers\Api\DriverController::class, 'store']);
    Route::put('/{id}', [App\Http\Controllers\Api\DriverController::class, 'update']);
    Route::delete('/{id}', [App\Http\Controllers\Api\DriverController::class, 'destroy']);
    Route::post('/{id}/toggle-active', [App\Http\Controllers\Api\DriverController::class, 'toggleActive']);
    Route::post('/{id}/toggle-online', [App\Http\Controllers\Api\DriverController::class, 'toggleOnline']);
    Route::post('/{id}/topup-wallet', [App\Http\Controllers\Api\DriverController::class, 'topupWallet']);
    Route::post('/bulk-delete', [App\Http\Controllers\Api\DriverController::class, 'bulkDelete']);
    Route::get('/{id}/orders-count', [App\Http\Controllers\Api\DriverController::class, 'getOrdersCount']);

    // Document management routes
    Route::get('/{id}/documents', [App\Http\Controllers\Api\DriverController::class, 'getDocuments']);
    Route::post('/{id}/documents/{docId}/verify', [App\Http\Controllers\Api\DriverController::class, 'verifyDocument']);
    Route::post('/{id}/documents/{docId}/upload', [App\Http\Controllers\Api\DriverController::class, 'uploadDocument']);
    Route::post('/{id}/send-notification', [App\Http\Controllers\Api\DriverController::class, 'sendNotification']);
});







/*
|--------------------------------------------------------------------------
| Booked Table API Routes (Firebase Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('booked-tables')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\BookedTableController::class, 'index']);
    Route::put('/{id}/status', [App\Http\Controllers\Api\BookedTableController::class, 'updateStatus']);
    Route::delete('/{id}', [App\Http\Controllers\Api\BookedTableController::class, 'destroy']);
    Route::get('/vendor/{vendorId}', [App\Http\Controllers\Api\BookedTableController::class, 'getVendorInfo']);
});

/*
|--------------------------------------------------------------------------
| Vendor Categories API Routes (Firebase Replacement)
|--------------------------------------------------------------------------
*/
Route::prefix('vendor-categories')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\VendorCategoryController::class, 'index']);
    Route::post('/', [App\Http\Controllers\Api\VendorCategoryController::class, 'store']);
    Route::get('/{id}', [App\Http\Controllers\Api\VendorCategoryController::class, 'show']);
    Route::put('/{id}', [App\Http\Controllers\Api\VendorCategoryController::class, 'update']);
    Route::delete('/{id}', [App\Http\Controllers\Api\VendorCategoryController::class, 'destroy']);
});
