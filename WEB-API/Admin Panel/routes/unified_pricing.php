<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Direct Unified Pricing Routes
|--------------------------------------------------------------------------
|
| Routes for the new direct unified pricing system without templates
|
*/

// API routes will be implemented later

// Web routes for admin interface - Direct Unified Pricing System
Route::prefix('unified-pricing')->middleware(['web', 'auth:admin'])->group(function () {

    // Main dashboard - Direct unified pricing without templates
    Route::get('/', [App\Http\Controllers\DirectUnifiedPricingController::class, 'index'])->name('unified-pricing.index');

    // Commission Profiles Management
    Route::get('/profiles', [App\Http\Controllers\DirectUnifiedPricingController::class, 'profiles'])->name('unified-pricing.profiles');
    Route::get('/profiles/create', [App\Http\Controllers\DirectUnifiedPricingController::class, 'createProfile'])->name('unified-pricing.profiles.create');
    Route::post('/profiles', [App\Http\Controllers\DirectUnifiedPricingController::class, 'storeProfile'])->name('unified-pricing.profiles.store');
    Route::get('/profiles/{id}/edit', [App\Http\Controllers\DirectUnifiedPricingController::class, 'editProfile'])->name('unified-pricing.profiles.edit');
    Route::put('/profiles/{id}', [App\Http\Controllers\DirectUnifiedPricingController::class, 'updateProfile'])->name('unified-pricing.profiles.update');
    Route::delete('/profiles/{id}', [App\Http\Controllers\DirectUnifiedPricingController::class, 'destroyProfile'])->name('unified-pricing.profiles.destroy');

    // Restaurant Integration Management
    Route::get('/restaurants', [App\Http\Controllers\DirectUnifiedPricingController::class, 'restaurants'])->name('unified-pricing.restaurants');

    // Commission Profiles Management (Simplified)
    Route::get('/commission-profiles', function() {
        return view('unified-pricing.commission-profiles');
    })->name('unified-pricing.commission-profiles')
      ->middleware('permission:unified-pricing');

    // Restaurant Pricing Management (New Integrated System)
    Route::get('/restaurant-management', function() {
        return view('unified-pricing.restaurant-management');
    })->name('unified-pricing.restaurant-management')
      ->middleware('permission:unified-pricing');

    // Analytics and Reports
    Route::get('/analytics', function() {
        return view('unified-pricing.analytics');
    })->name('unified-pricing.analytics');

    // AJAX/API routes for profile assignment
    Route::post('/auto-assign-profiles', function() {
        return response()->json([
            'success' => true,
            'message' => 'Auto-assignment completed successfully',
            'results' => [
                'successful' => [],
                'failed' => []
            ]
        ]);
    })->name('unified-pricing.auto-assign-profiles');

    Route::post('/evaluate-upgrades', function() {
        return response()->json([
            'success' => true,
            'message' => 'Upgrade evaluation completed successfully',
            'results' => [
                'evaluated' => 0,
                'upgraded' => 0,
                'no_change' => 0
            ]
        ]);
    })->name('unified-pricing.evaluate-upgrades');
});