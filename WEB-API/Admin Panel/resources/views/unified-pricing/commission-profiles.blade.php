@extends('layouts.app')

@section('content')
<div class="page-wrapper">
    <div class="container-fluid">
        <!-- Breadcrumb -->
        <div class="row page-titles">
            <div class="col-md-5 align-self-center">
                <h3 class="text-themecolor">{{ trans('lang.commission_profiles') }}</h3>
            </div>
            <div class="col-md-7 align-self-center text-end">
                <div class="d-flex justify-content-end align-items-center">
                    <ol class="breadcrumb justify-content-end">
                        <li class="breadcrumb-item"><a href="{{ url('/dashboard') }}">{{ trans('lang.dashboard') }}</a></li>
                        <li class="breadcrumb-item"><a href="{{ url('/unified-pricing') }}">{{ trans('lang.unified_pricing') }}</a></li>
                        <li class="breadcrumb-item active">{{ trans('lang.commission_profiles') }}</li>
                    </ol>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4 class="card-title mb-0">{{ trans('lang.commission_profiles_management') }}</h4>
                        <button type="button" class="btn btn-primary" onclick="commissionProfiles.showCreateModal()">
                            <i class="fas fa-plus me-2"></i>{{ trans('lang.create_profile') }}
                        </button>
                    </div>
                    <div class="card-body">
                        <!-- Filters -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <select class="form-select" id="profileTypeFilter" onchange="commissionProfiles.filterProfiles()">
                                    <option value="">{{ trans('lang.all_types') }}</option>
                                    <option value="standard">{{ trans('lang.standard') }}</option>
                                    <option value="premium">{{ trans('lang.premium') }}</option>
                                    <option value="custom">{{ trans('lang.custom') }}</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <select class="form-select" id="statusFilter" onchange="commissionProfiles.filterProfiles()">
                                    <option value="">{{ trans('lang.all_statuses') }}</option>
                                    <option value="active">{{ trans('lang.active') }}</option>
                                    <option value="inactive">{{ trans('lang.inactive') }}</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="searchInput" 
                                           placeholder="{{ trans('lang.search_profiles') }}" 
                                           onkeyup="commissionProfiles.searchProfiles()">
                                    <button class="btn btn-outline-secondary" type="button">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Profiles Table -->
                        <div class="table-responsive">
                            <table class="table table-hover" id="profilesTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>{{ trans('lang.profile_name') }}</th>
                                        <th>{{ trans('lang.type') }}</th>
                                        <th>{{ trans('lang.admin_commission') }}</th>
                                        <th>{{ trans('lang.driver_commission') }}</th>
                                        <th>{{ trans('lang.delivery_charge') }}</th>
                                        <th>{{ trans('lang.restaurants_count') }}</th>
                                        <th>{{ trans('lang.status') }}</th>
                                        <th>{{ trans('lang.actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody id="profilesTableBody">
                                    <!-- Profiles will be loaded here -->
                                </tbody>
                            </table>
                        </div>

                        <!-- Loading State -->
                        <div id="loadingState" class="text-center py-4" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">{{ trans('lang.loading') }}</span>
                            </div>
                            <p class="mt-2">{{ trans('lang.loading_profiles') }}</p>
                        </div>

                        <!-- Empty State -->
                        <div id="emptyState" class="text-center py-5" style="display: none;">
                            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{{ trans('lang.no_profiles_found') }}</h5>
                            <p class="text-muted">{{ trans('lang.create_first_profile') }}</p>
                            <button type="button" class="btn btn-primary" onclick="commissionProfiles.showCreateModal()">
                                <i class="fas fa-plus me-2"></i>{{ trans('lang.create_profile') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create/Edit Profile Modal -->
<div class="modal fade" id="profileModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="profileModalTitle">{{ trans('lang.create_profile') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="profileForm" onsubmit="commissionProfiles.handleSubmit(event)">
                <div class="modal-body">
                    <input type="hidden" id="profileId" name="profile_id">
                    
                    <!-- Basic Information -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="display_name" class="form-label">{{ trans('lang.profile_name') }} *</label>
                            <input type="text" class="form-control" id="display_name" name="display_name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="profile_type" class="form-label">{{ trans('lang.profile_type') }} *</label>
                            <select class="form-select" id="profile_type" name="profile_type" required>
                                <option value="">{{ trans('lang.select_type') }}</option>
                                <option value="standard">{{ trans('lang.standard') }}</option>
                                <option value="premium">{{ trans('lang.premium') }}</option>
                                <option value="custom">{{ trans('lang.custom') }}</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">{{ trans('lang.description') }}</label>
                        <textarea class="form-control" id="description" name="description" rows="2"></textarea>
                    </div>

                    <!-- Simplified Commission Rates -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="admin_commission_rate" class="form-label">عمولة الإدارة (%) *</label>
                            <input type="number" class="form-control" id="admin_commission_rate"
                                   name="admin_commission_rate" min="0" max="50" step="0.1" value="10" required>
                            <small class="text-muted">العمولة المستحقة للإدارة من كل طلب</small>
                        </div>
                        <div class="col-md-4">
                            <label for="driver_commission_rate" class="form-label">عمولة السائق (%) *</label>
                            <input type="number" class="form-control" id="driver_commission_rate"
                                   name="driver_commission_rate" min="0" max="30" step="0.1" value="15" required>
                            <small class="text-muted">العمولة المستحقة للسائق من كل طلب</small>
                        </div>
                        <div class="col-md-4">
                            <label for="base_delivery_charge" class="form-label">رسوم التوصيل الأساسية *</label>
                            <input type="number" class="form-control" id="base_delivery_charge"
                                   name="base_delivery_charge" min="0" step="0.5" value="5" required>
                            <small class="text-muted">الرسوم الأساسية للتوصيل (ريال سعودي)</small>
                        </div>
                    </div>

                    <!-- Status -->
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                        <label class="form-check-label" for="is_active">
                            {{ trans('lang.active_profile') }}
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ trans('lang.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">
                        <span id="submitButtonText">{{ trans('lang.create_profile') }}</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ trans('lang.confirm_delete') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>{{ trans('lang.delete_profile_confirmation') }}</p>
                <p class="text-danger"><strong id="deleteProfileName"></strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ trans('lang.cancel') }}</button>
                <button type="button" class="btn btn-danger" onclick="commissionProfiles.confirmDelete()">{{ trans('lang.delete') }}</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
/* Commission Profiles Styles */
.profile-type-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.commission-rate {
    font-weight: 600;
    color: #28a745;
}

.delivery-charge {
    font-weight: 600;
    color: #007bff;
}

.restaurants-count {
    font-weight: 600;
    color: #6c757d;
}

.status-active {
    color: #28a745;
}

.status-inactive {
    color: #dc3545;
}

/* Responsive table */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}
</style>
@endsection

@section('scripts')
<script src="{{ asset('js/unified-pricing/commission-profiles.js') }}"></script>
<script>
$(document).ready(function() {
    console.log('Initializing Commission Profiles Management...');
    window.commissionProfiles = new CommissionProfilesManagement();
});
</script>
@endsection
