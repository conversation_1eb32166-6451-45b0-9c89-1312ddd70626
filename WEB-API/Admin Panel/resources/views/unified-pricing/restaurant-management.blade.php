@extends('layouts.app')

@section('title', 'إدارة المطاعم والتسعير')

@section('content')
<div class="page-wrapper">
    <div class="row page-titles">
        <div class="col-md-5 align-self-center">
            <h3 class="text-themecolor">إدارة المطاعم والتسعير</h3>
        </div>
        <div class="col-md-7 align-self-center">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('unified-pricing.index') }}">{{ trans('unified_pricing.breadcrumb_dashboard') }}</a></li>
                <li class="breadcrumb-item active">{{ trans('unified_pricing.restaurant_management') }}</li>
            </ol>
        </div>
        <div>
        </div>
    </div>
    <div class="container-fluid">
       <div class="admin-top-section">
        <div class="row">
            <div class="col-12">
                <div class="d-flex top-title-section pb-4 justify-content-between">
                    <div class="d-flex top-title-left align-self-center">
                        <span class="icon mr-3"><img src="{{ asset('images/restaurant.png') }}" alt="Restaurant Icon"></span>
                        <h3 class="mb-0">إدارة المطاعم والتسعير</h3>
                        <span class="counter ml-3" id="restaurantCount">0</span>
                    </div>
                    <div class="d-flex top-title-right align-self-center">
                        <div class="select-box pl-3">
                            <button type="button" class="btn btn-success mr-2" onclick="autoAssignProfiles()">
                                <i class="fas fa-magic mr-2"></i>تعيين تلقائي
                            </button>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createProfileModal">
                                <i class="fas fa-plus mr-2"></i>إنشاء ملف مخصص
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
       </div>

    <!-- Statistics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-6 col-lg-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="card-title mb-1 fs-5 fs-md-4" id="totalRestaurants">0</h4>
                            <p class="card-text mb-0 small">إجمالي المطاعم</p>
                        </div>
                        <div class="ms-2">
                            <i class="fas fa-store fa-lg fa-md-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-6 col-lg-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="card-title mb-1 fs-5 fs-md-4" id="activeProfiles">0</h4>
                            <p class="card-text mb-0 small">الملفات النشطة</p>
                        </div>
                        <div class="ms-2">
                            <i class="fas fa-check-circle fa-lg fa-md-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-6 col-lg-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="card-title mb-1 fs-5 fs-md-4" id="customProfiles">0</h4>
                            <p class="card-text mb-0 small">الملفات المخصصة</p>
                        </div>
                        <div class="ms-2">
                            <i class="fas fa-cog fa-lg fa-md-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-6 col-lg-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="card-title mb-1 fs-5 fs-md-4" id="standardProfiles">0</h4>
                            <p class="card-text mb-0 small">الملفات القياسية</p>
                        </div>
                        <div class="ms-2">
                            <i class="fas fa-layer-group fa-lg fa-md-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Restaurants Table -->
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center gap-3">
                        <h5 class="card-title mb-0">قائمة المطاعم</h5>
                        <div class="d-flex flex-wrap gap-2">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="toggleBulkMode()">
                                <i class="fas fa-tasks me-1"></i>العمليات المجمعة
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm" onclick="showAnalytics()">
                                <i class="fas fa-chart-bar me-1"></i>التحليلات
                            </button>
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="exportData()">
                                <i class="fas fa-download me-1"></i>تصدير
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0 p-md-3">
                    <!-- Search and Filter -->
                    <div class="row g-2 mb-3 px-3 px-md-0">
                        <div class="col-12 col-md-4">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="searchRestaurants" placeholder="البحث في المطاعم...">
                            </div>
                        </div>
                        <div class="col-6 col-md-2">
                            <select class="form-select" id="filterProfileType">
                                <option value="">جميع الأنواع</option>
                                <option value="standard">قياسي</option>
                                <option value="premium">مميز</option>
                                <option value="custom">مخصص</option>
                            </select>
                        </div>
                        <div class="col-6 col-md-2">
                            <select class="form-select" id="filterStatus">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                        <div class="col-6 col-md-2">
                            <button type="button" class="btn btn-outline-secondary w-100" onclick="resetFilters()">
                                <i class="fas fa-undo me-1"></i>إعادة تعيين
                            </button>
                        </div>
                        <div class="col-6 col-md-2">
                            <button type="button" class="btn btn-outline-success w-100" onclick="refreshData()">
                                <i class="fas fa-sync me-1"></i>تحديث
                            </button>
                        </div>
                    </div>

                    <!-- Bulk Operations Bar (Hidden by default) -->
                    <div id="bulkOperationsBar" class="alert alert-info mx-3 mx-md-0" style="display: none;">
                        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center gap-3">
                            <div>
                                <strong>العمليات المجمعة:</strong>
                                <span id="selectedCount">0</span> مطعم محدد
                            </div>
                            <div class="d-flex flex-wrap gap-2">
                                <select class="form-select form-select-sm" id="bulkProfileSelect" style="width: auto;">
                                    <option value="">اختر ملف العمولة</option>
                                </select>
                                <button type="button" class="btn btn-sm btn-primary" onclick="performBulkAssign()">
                                    <i class="fas fa-link me-1"></i>تعيين ملف العمولة
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleBulkMode()">
                                    <i class="fas fa-times me-1"></i>إلغاء
                                </button>
                            </div>
                        </div>
                    </div>

                         <div class="table-responsive m-t-10">
                            <table id="restaurantsTable" class="display nowrap table table-hover table-striped table-bordered table table-striped" cellspacing="0" width="100%">
                                <thead>
                                    <tr>
                                        <th class="d-none" id="bulkSelectHeader">
                                            <input type="checkbox" class="form-check-input" id="selectAll">
                                        </th>
                                        <th class="text-nowrap">اسم المطعم</th>
                                        <th class="d-none d-md-table-cell">البريد الإلكتروني</th>
                                        <th class="text-nowrap">ملف العمولة المعين</th>
                                        <th class="d-none d-lg-table-cell text-nowrap">معدلات العمولة</th>
                                        <th class="d-none d-lg-table-cell text-nowrap">رسوم التوصيل</th>
                                        <th class="text-nowrap" style="min-width: 300px;">تغيير ملف العمولة</th>
                                    </tr>
                                </thead>
                                <tbody id="restaurantsTableBody">
                                    <!-- Data will be loaded via JavaScript -->
                                </tbody>
                            </table>
                        </div>

                    <!-- Loading Spinner -->
                    <div class="text-center py-4" id="loadingSpinner" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">{{ trans('unified_pricing.loading') }}</span>
                        </div>
                    </div>

                    <!-- No Data Message -->
                    <div class="text-center py-4" id="noDataMessage" style="display: none;">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">{{ trans('unified_pricing.no_restaurants_found') }}</p>
                    </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </div>
</div>

<!-- Create Profile Modal -->
<div class="modal fade" id="createProfileModal" tabindex="-1" aria-labelledby="createProfileModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createProfileModalLabel">{{ trans('unified_pricing.create_custom_profile') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="createProfileForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="restaurant_id" class="form-label">{{ trans('unified_pricing.restaurant') }} <span class="text-danger">*</span></label>
                                <select class="form-select" id="restaurant_id" name="restaurant_id" required>
                                    <option value="">{{ trans('unified_pricing.select_restaurant') }}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="profile_name" class="form-label">{{ trans('unified_pricing.profile_name') }} <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="profile_name" name="profile_name" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">{{ trans('unified_pricing.description') }}</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="custom_admin_commission_rate" class="form-label">{{ trans('unified_pricing.admin_commission_rate') }} (%)</label>
                                <input type="number" class="form-control" id="custom_admin_commission_rate" name="custom_admin_commission_rate" min="0" max="100" step="0.01">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="custom_driver_commission_rate" class="form-label">{{ trans('unified_pricing.driver_commission_rate') }} (%)</label>
                                <input type="number" class="form-control" id="custom_driver_commission_rate" name="custom_driver_commission_rate" min="0" max="100" step="0.01">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="custom_delivery_base_charge" class="form-label">{{ trans('unified_pricing.delivery_base_charge') }} ({{ trans('unified_pricing.currency') }})</label>
                                <input type="number" class="form-control" id="custom_delivery_base_charge" name="custom_delivery_base_charge" min="0" step="0.01">
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        {{ trans('unified_pricing.custom_rates_note') }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ trans('unified_pricing.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>{{ trans('unified_pricing.create_profile') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Profile Modal -->
<div class="modal fade" id="editProfileModal" tabindex="-1" aria-labelledby="editProfileModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editProfileModalLabel">{{ trans('unified_pricing.edit_profile') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editProfileForm">
                <input type="hidden" id="edit_profile_id" name="profile_id">
                <div class="modal-body">
                    <!-- Same form fields as create modal -->
                    <div class="mb-3">
                        <label for="edit_profile_name" class="form-label">{{ trans('unified_pricing.profile_name') }} <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_profile_name" name="profile_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">{{ trans('unified_pricing.description') }}</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_custom_admin_commission_rate" class="form-label">{{ trans('unified_pricing.admin_commission_rate') }} (%)</label>
                                <input type="number" class="form-control" id="edit_custom_admin_commission_rate" name="custom_admin_commission_rate" min="0" max="100" step="0.01">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_custom_driver_commission_rate" class="form-label">{{ trans('unified_pricing.driver_commission_rate') }} (%)</label>
                                <input type="number" class="form-control" id="edit_custom_driver_commission_rate" name="custom_driver_commission_rate" min="0" max="100" step="0.01">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_custom_delivery_base_charge" class="form-label">{{ trans('unified_pricing.delivery_base_charge') }} ({{ trans('unified_pricing.currency') }})</label>
                                <input type="number" class="form-control" id="edit_custom_delivery_base_charge" name="custom_delivery_base_charge" min="0" step="0.01">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                            <label class="form-check-label" for="edit_is_active">
                                {{ trans('unified_pricing.profile_active') }}
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ trans('unified_pricing.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>{{ trans('unified_pricing.update_profile') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Pricing Calculator Modal -->
<div class="modal fade" id="calculatorModal" tabindex="-1" aria-labelledby="calculatorModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="calculatorModalLabel">{{ trans('unified_pricing.pricing_calculator') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="calculatorForm">
                    <input type="hidden" id="calc_restaurant_id" name="restaurant_id">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="order_total" class="form-label">{{ trans('unified_pricing.order_total') }} ({{ trans('unified_pricing.currency') }}) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="order_total" name="order_total" min="0" step="0.01" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="distance_km" class="form-label">{{ trans('unified_pricing.distance_km') }}</label>
                                <input type="number" class="form-control" id="distance_km" name="distance_km" min="0" step="0.1">
                            </div>
                        </div>
                    </div>
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-calculator me-2"></i>{{ trans('unified_pricing.calculate') }}
                        </button>
                    </div>
                </form>

                <div id="calculationResults" class="mt-4" style="display: none;">
                    <h6>{{ trans('unified_pricing.calculation_results') }}</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <tbody id="resultsTableBody">
                                <!-- Results will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Analytics Modal -->
<div class="modal fade" id="analyticsModal" tabindex="-1" aria-labelledby="analyticsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="analyticsModalLabel">تحليلات المطاعم والتسعير</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h4 id="analyticsActiveRestaurants">0</h4>
                                <p class="mb-0">مطاعم نشطة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h4 id="analyticsTotalRevenue">0 ر.س</h4>
                                <p class="mb-0">إجمالي الإيرادات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h4 id="analyticsTotalCommission">0 ر.س</h4>
                                <p class="mb-0">إجمالي العمولات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h4 id="analyticsAverageOrder">0 ر.س</h4>
                                <p class="mb-0">متوسط قيمة الطلب</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">توزيع أنواع الملفات</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="profileTypesChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">أفضل المطاعم أداءً</h6>
                            </div>
                            <div class="card-body">
                                <div id="topPerformingRestaurants">
                                    <!-- Will be populated by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="exportAnalytics()">
                    <i class="fas fa-download me-2"></i>تصدير التحليلات
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
/* RTL Support */
[dir="rtl"] .table th,
[dir="rtl"] .table td {
    text-align: right;
}

[dir="rtl"] .breadcrumb-item + .breadcrumb-item::before {
    float: left;
    padding-right: 0.5rem;
    padding-left: 0;
    content: var(--bs-breadcrumb-divider, "‹");
}

[dir="rtl"] .btn-group .btn {
    margin-left: 0;
    margin-right: 0.1rem;
}

[dir="rtl"] .input-group .input-group-text {
    border-left: 1px solid #ced4da;
    border-right: 0;
}

[dir="rtl"] .modal-header .btn-close {
    margin: -0.5rem 0 -0.5rem auto;
}

[dir="rtl"] .d-flex .me-2 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

[dir="rtl"] .ms-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
}

/* Responsive Design */
.badge-custom {
    font-size: 0.75em;
}

.commission-rates {
    font-size: 0.85em;
    line-height: 1.2;
}

.table th {
    border-top: none;
    font-weight: 600;
}

.modal-lg {
    max-width: 800px;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }

    .table-responsive {
        border: none;
    }

    .btn-group-sm > .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    .commission-rates {
        font-size: 0.75em;
    }

    .badge {
        font-size: 0.65em;
    }
}

/* Loading and Animation States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Bulk Operations */
.bulk-select-checkbox {
    transform: scale(1.2);
}

.selected-row {
    background-color: rgba(13, 110, 253, 0.1) !important;
}

/* Responsive Table Enhancements */
@media (max-width: 992px) {
    .table td {
        padding: 0.5rem 0.25rem;
    }

    .btn-group .btn {
        padding: 0.25rem 0.5rem;
        margin: 0.1rem;
    }
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1055;
}

[dir="rtl"] .toast-container {
    right: auto;
    left: 20px;
}

/* Performance Metrics Cards */
.metric-card {
    transition: transform 0.2s ease-in-out;
}

.metric-card:hover {
    transform: translateY(-2px);
}

/* Custom Scrollbar for Table */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Commission Profile Assignment Interface */
.profile-assignment-container {
    min-width: 250px;
}

.profile-assignment-container .input-group {
    flex-wrap: nowrap;
}

.profile-assignment-container .form-select {
    min-width: 180px;
    font-size: 0.875rem;
}

.profile-assignment-container .btn {
    white-space: nowrap;
}

/* Restaurant without profile styling */
.restaurant-no-profile {
    background-color: rgba(255, 193, 7, 0.1);
}

.restaurant-no-profile:hover {
    background-color: rgba(255, 193, 7, 0.2);
}

/* Restaurant with profile styling */
.restaurant-with-profile {
    background-color: rgba(25, 135, 84, 0.05);
}

.restaurant-with-profile:hover {
    background-color: rgba(25, 135, 84, 0.1);
}

/* Mobile optimizations for assignment interface */
@media (max-width: 768px) {
    .profile-assignment-container {
        min-width: 100%;
    }

    .profile-assignment-container .input-group {
        flex-direction: column;
        gap: 0.5rem;
    }

    .profile-assignment-container .form-select {
        min-width: 100%;
    }

    .profile-assignment-container .btn {
        width: 100%;
    }
}
</style>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{{ asset('js/unified-pricing/restaurant-management.js') }}"></script>
<script type="text/javascript">
// Ensure jQuery is loaded before initializing
$(document).ready(function() {
    console.log('jQuery loaded, initializing Restaurant Management...');

    // Check if jQuery is available
    if (typeof $ === 'undefined') {
        console.error('jQuery is not loaded!');
        return;
    }

    // Initialize Restaurant Management
    try {
        window.restaurantMgmt = new RestaurantManagement();
        console.log('Restaurant Management initialized successfully');
    } catch (error) {
        console.error('Error initializing Restaurant Management:', error);
    }
});
</script>
@endsection
