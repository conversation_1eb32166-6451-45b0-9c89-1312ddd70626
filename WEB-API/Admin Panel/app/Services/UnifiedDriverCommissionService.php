<?php

namespace App\Services;

use App\Models\Order;
use App\Models\UnifiedUser;
use App\Models\RestaurantPricingProfile;
use App\Models\CommissionProfile;
use App\Models\DriverCommissionAssignment;
use App\Models\DriverWallet;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

/**
 * Unified Driver Commission Service
 * Handles driver commission distribution based on restaurant's assigned commission profile
 */
class UnifiedDriverCommissionService
{
    /**
     * Calculate and distribute driver commission for completed order
     */
    public function processOrderCommission(Order $order): array
    {
        try {
            DB::beginTransaction();

            // Get restaurant's commission profile
            $restaurant = UnifiedUser::find($order->vendorID);
            if (!$restaurant) {
                throw new \Exception('Restaurant not found');
            }

            $restaurantProfile = $restaurant->restaurantPricingProfile;
            if (!$restaurantProfile) {
                throw new \Exception('Restaurant does not have a pricing profile');
            }

            // Get driver
            $driver = UnifiedUser::find($order->driverID);
            if (!$driver) {
                throw new \Exception('Driver not found');
            }

            // Calculate commission based on restaurant's profile
            $commissionData = $this->calculateDriverCommission($order, $restaurantProfile);

            // Add commission to driver wallet
            $this->addCommissionToDriverWallet($driver, $commissionData, $order);

            // Log the commission distribution
            $this->logCommissionDistribution($order, $driver, $restaurant, $commissionData);

            DB::commit();

            return [
                'success' => true,
                'commission_amount' => $commissionData['commission_amount'],
                'calculation_details' => $commissionData,
                'driver_id' => $driver->id,
                'restaurant_id' => $restaurant->id
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to process driver commission', [
                'order_id' => $order->id,
                'driver_id' => $order->driverID,
                'restaurant_id' => $order->vendorID,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Calculate driver commission based on restaurant's commission profile
     */
    private function calculateDriverCommission(Order $order, RestaurantPricingProfile $restaurantProfile): array
    {
        // Get the commission profile linked to the restaurant
        $commissionProfile = $restaurantProfile->getLinkedCommissionProfile();
        
        if (!$commissionProfile) {
            // Fallback to restaurant profile's custom rates
            $driverRate = $restaurantProfile->custom_driver_commission_rate ?? 10.0;
        } else {
            $driverRate = $commissionProfile->driver_commission_rate;
        }

        // Calculate base commission (percentage of order total)
        $orderTotal = $order->total ?? 0;
        $baseCommission = ($orderTotal * $driverRate) / 100;

        // Add delivery charge if applicable
        $deliveryCommission = $order->deliveryCharge ?? 0;

        // Calculate distance-based bonus if available
        $distanceBonus = $this->calculateDistanceBonus($order, $restaurantProfile);

        // Calculate total commission
        $totalCommission = $baseCommission + $deliveryCommission + $distanceBonus;

        // Apply minimum and maximum limits
        $finalCommission = $this->applyCommissionLimits($totalCommission, $restaurantProfile);

        return [
            'commission_amount' => $finalCommission,
            'base_commission' => $baseCommission,
            'delivery_commission' => $deliveryCommission,
            'distance_bonus' => $distanceBonus,
            'driver_rate' => $driverRate,
            'order_total' => $orderTotal,
            'calculation_method' => 'unified_pricing_profile',
            'restaurant_profile_id' => $restaurantProfile->id,
            'commission_profile_id' => $commissionProfile?->id
        ];
    }

    /**
     * Calculate distance-based bonus
     */
    private function calculateDistanceBonus(Order $order, RestaurantPricingProfile $restaurantProfile): float
    {
        $distance = $order->distance ?? 0;
        if ($distance <= 0) {
            return 0;
        }

        $unifiedSetting = $restaurantProfile->unifiedPricingSetting;
        $perKmRate = $unifiedSetting?->per_km_rate ?? 0;

        return $distance * $perKmRate;
    }

    /**
     * Apply commission limits
     */
    private function applyCommissionLimits(float $commission, RestaurantPricingProfile $restaurantProfile): float
    {
        $unifiedSetting = $restaurantProfile->unifiedPricingSetting;
        
        if ($unifiedSetting) {
            // Apply minimum commission
            if ($unifiedSetting->min_driver_commission && $commission < $unifiedSetting->min_driver_commission) {
                $commission = $unifiedSetting->min_driver_commission;
            }

            // Apply maximum commission
            if ($unifiedSetting->max_driver_commission && $commission > $unifiedSetting->max_driver_commission) {
                $commission = $unifiedSetting->max_driver_commission;
            }
        }

        return $commission;
    }

    /**
     * Add commission to driver wallet
     */
    private function addCommissionToDriverWallet(UnifiedUser $driver, array $commissionData, Order $order): void
    {
        // Get or create driver wallet
        $wallet = DriverWallet::firstOrCreate(
            ['driver_id' => $driver->id],
            ['balance' => 0, 'total_earned' => 0]
        );

        // Add commission to wallet
        $wallet->increment('balance', $commissionData['commission_amount']);
        $wallet->increment('total_earned', $commissionData['commission_amount']);

        // Create wallet transaction record
        $wallet->transactions()->create([
            'type' => 'commission',
            'amount' => $commissionData['commission_amount'],
            'description' => "Commission for order #{$order->id}",
            'order_id' => $order->id,
            'metadata' => $commissionData
        ]);
    }

    /**
     * Log commission distribution for audit trail
     */
    private function logCommissionDistribution(Order $order, UnifiedUser $driver, UnifiedUser $restaurant, array $commissionData): void
    {
        Log::info('Driver commission distributed', [
            'order_id' => $order->id,
            'driver_id' => $driver->id,
            'driver_name' => $driver->firstName . ' ' . $driver->lastName,
            'restaurant_id' => $restaurant->id,
            'restaurant_name' => $restaurant->firstName . ' ' . $restaurant->lastName,
            'commission_amount' => $commissionData['commission_amount'],
            'calculation_details' => $commissionData,
            'timestamp' => now()
        ]);
    }

    /**
     * Get driver commission summary for a specific period
     */
    public function getDriverCommissionSummary(string $driverId, string $startDate, string $endDate): array
    {
        $driver = UnifiedUser::find($driverId);
        if (!$driver) {
            throw new \Exception('Driver not found');
        }

        $wallet = DriverWallet::where('driver_id', $driverId)->first();
        if (!$wallet) {
            return [
                'driver_id' => $driverId,
                'current_balance' => 0,
                'total_earned' => 0,
                'period_earnings' => 0,
                'orders_count' => 0
            ];
        }

        // Get period earnings
        $periodEarnings = $wallet->transactions()
            ->where('type', 'commission')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('amount');

        $ordersCount = $wallet->transactions()
            ->where('type', 'commission')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        return [
            'driver_id' => $driverId,
            'driver_name' => $driver->firstName . ' ' . $driver->lastName,
            'current_balance' => $wallet->balance,
            'total_earned' => $wallet->total_earned,
            'period_earnings' => $periodEarnings,
            'orders_count' => $ordersCount,
            'period_start' => $startDate,
            'period_end' => $endDate
        ];
    }

    /**
     * Get commission breakdown by restaurant for a driver
     */
    public function getDriverCommissionByRestaurant(string $driverId, string $startDate, string $endDate): array
    {
        $transactions = DB::table('driver_wallet_transactions as dwt')
            ->join('orders as o', 'dwt.order_id', '=', 'o.id')
            ->join('users as r', 'o.vendorID', '=', 'r.id')
            ->where('dwt.driver_id', $driverId)
            ->where('dwt.type', 'commission')
            ->whereBetween('dwt.created_at', [$startDate, $endDate])
            ->select([
                'r.id as restaurant_id',
                'r.firstName as restaurant_name',
                DB::raw('COUNT(dwt.id) as orders_count'),
                DB::raw('SUM(dwt.amount) as total_commission'),
                DB::raw('AVG(dwt.amount) as avg_commission')
            ])
            ->groupBy('r.id', 'r.firstName')
            ->orderBy('total_commission', 'desc')
            ->get();

        return $transactions->toArray();
    }

    /**
     * Recalculate commission for an order (for corrections)
     */
    public function recalculateOrderCommission(Order $order): array
    {
        // This method can be used to recalculate commission if restaurant profile changes
        // or if there are corrections needed
        
        return $this->processOrderCommission($order);
    }
}
