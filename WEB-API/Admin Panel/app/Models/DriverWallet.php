<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

/**
 * Driver Wallet Model
 * Simplified driver wallet management for unified pricing system
 */
class DriverWallet extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'driver_wallets';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'id',
        'driver_id',
        'balance',
        'total_earned',
        'total_withdrawn',
        'is_active',
        'currency',
        'last_transaction_at',
    ];

    protected $casts = [
        'balance' => 'decimal:2',
        'total_earned' => 'decimal:2',
        'total_withdrawn' => 'decimal:2',
        'is_active' => 'boolean',
        'last_transaction_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Boot method to generate UUID
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($wallet) {
            if (empty($wallet->id)) {
                $wallet->id = (string) Str::uuid();
            }
            
            if (empty($wallet->currency)) {
                $wallet->currency = 'SAR';
            }
        });
    }

    /**
     * Relationship: Driver (belongs to)
     */
    public function driver()
    {
        return $this->belongsTo(UnifiedUser::class, 'driver_id');
    }

    /**
     * Relationship: Wallet Transactions
     */
    public function transactions()
    {
        return $this->hasMany(DriverWalletTransaction::class, 'wallet_id');
    }

    /**
     * Add commission to wallet
     */
    public function addCommission(float $amount, string $orderId, array $metadata = []): DriverWalletTransaction
    {
        $balanceBefore = $this->balance;
        
        // Update wallet balance
        $this->increment('balance', $amount);
        $this->increment('total_earned', $amount);
        $this->update(['last_transaction_at' => now()]);

        // Create transaction record
        return $this->transactions()->create([
            'driver_id' => $this->driver_id,
            'type' => 'commission',
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $balanceBefore + $amount,
            'description' => "Commission for order #{$orderId}",
            'order_id' => $orderId,
            'metadata' => $metadata,
            'status' => 'completed'
        ]);
    }

    /**
     * Withdraw amount from wallet
     */
    public function withdraw(float $amount, string $reason = 'withdrawal', array $metadata = []): DriverWalletTransaction
    {
        if ($this->balance < $amount) {
            throw new \Exception('Insufficient wallet balance');
        }

        $balanceBefore = $this->balance;
        
        // Update wallet balance
        $this->decrement('balance', $amount);
        $this->increment('total_withdrawn', $amount);
        $this->update(['last_transaction_at' => now()]);

        // Create transaction record
        return $this->transactions()->create([
            'driver_id' => $this->driver_id,
            'type' => 'withdrawal',
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $balanceBefore - $amount,
            'description' => "Withdrawal: {$reason}",
            'metadata' => $metadata,
            'status' => 'completed'
        ]);
    }

    /**
     * Get available balance (excluding any holds)
     */
    public function getAvailableBalance(): float
    {
        return $this->balance;
    }

    /**
     * Get commission earnings for a specific period
     */
    public function getCommissionEarnings(string $startDate, string $endDate): float
    {
        return $this->transactions()
            ->where('type', 'commission')
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('amount');
    }

    /**
     * Get total orders count for a specific period
     */
    public function getOrdersCount(string $startDate, string $endDate): int
    {
        return $this->transactions()
            ->where('type', 'commission')
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();
    }

    /**
     * Get average commission per order for a specific period
     */
    public function getAverageCommission(string $startDate, string $endDate): float
    {
        $totalCommission = $this->getCommissionEarnings($startDate, $endDate);
        $ordersCount = $this->getOrdersCount($startDate, $endDate);

        return $ordersCount > 0 ? $totalCommission / $ordersCount : 0;
    }

    /**
     * Check if wallet is active
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * Activate wallet
     */
    public function activate(): void
    {
        $this->update(['is_active' => true]);
    }

    /**
     * Deactivate wallet
     */
    public function deactivate(): void
    {
        $this->update(['is_active' => false]);
    }

    /**
     * Get wallet summary
     */
    public function getSummary(): array
    {
        return [
            'driver_id' => $this->driver_id,
            'balance' => $this->balance,
            'total_earned' => $this->total_earned,
            'total_withdrawn' => $this->total_withdrawn,
            'available_balance' => $this->getAvailableBalance(),
            'is_active' => $this->is_active,
            'currency' => $this->currency,
            'last_transaction_at' => $this->last_transaction_at,
            'created_at' => $this->created_at
        ];
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForDriver($query, $driverId)
    {
        return $query->where('driver_id', $driverId);
    }

    public function scopeWithBalance($query)
    {
        return $query->where('balance', '>', 0);
    }
}
