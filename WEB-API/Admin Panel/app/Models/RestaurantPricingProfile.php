<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

/**
 * Restaurant Pricing Profile Model
 * Direct integration between restaurants and unified pricing settings
 */
class RestaurantPricingProfile extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'restaurant_pricing_profiles';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'id',
        'restaurant_id',
        'unified_pricing_setting_id',
        'profile_name',
        'description',
        'profile_type',
        'custom_admin_commission_rate',
        'custom_driver_commission_rate',
        'custom_delivery_base_charge',
        'is_active',
        'activated_at',
        'expires_at',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'custom_admin_commission_rate' => 'decimal:2',
        'custom_driver_commission_rate' => 'decimal:2',
        'custom_delivery_base_charge' => 'decimal:2',
        'is_active' => 'boolean',
        'activated_at' => 'datetime',
        'expires_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Boot method to generate UUID
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->id)) {
                $model->id = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the restaurant that owns this pricing profile
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'restaurant_id');
    }

    /**
     * Get the unified pricing setting for this profile
     */
    public function unifiedPricingSetting(): BelongsTo
    {
        return $this->belongsTo(UnifiedPricingSetting::class, 'unified_pricing_setting_id');
    }

    /**
     * Get the user who created this profile
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this profile
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope to get active profiles only
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get profiles for a specific restaurant
     */
    public function scopeForRestaurant($query, $restaurantId)
    {
        return $query->where('restaurant_id', $restaurantId);
    }

    /**
     * Scope to get profiles by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('profile_type', $type);
    }

    /**
     * Get the effective commission rates (custom or from unified setting)
     */
    public function getEffectiveCommissionRates(): array
    {
        $unifiedSetting = $this->unifiedPricingSetting;
        
        return [
            'admin_commission_rate' => $this->custom_admin_commission_rate ?? $unifiedSetting->admin_commission_rate,
            'driver_commission_rate' => $this->custom_driver_commission_rate ?? $unifiedSetting->driver_commission_rate,
            'delivery_base_charge' => $this->custom_delivery_base_charge ?? $unifiedSetting->base_delivery_charge,
        ];
    }

    /**
     * Get the effective delivery charge settings
     */
    public function getEffectiveDeliverySettings(): array
    {
        $unifiedSetting = $this->unifiedPricingSetting;
        
        return [
            'delivery_charge_type' => $unifiedSetting->delivery_charge_type,
            'base_delivery_charge' => $this->custom_delivery_base_charge ?? $unifiedSetting->base_delivery_charge,
            'per_km_delivery_rate' => $unifiedSetting->per_km_delivery_rate,
            'min_delivery_charge' => $unifiedSetting->min_delivery_charge,
            'max_delivery_charge' => $unifiedSetting->max_delivery_charge,
            'free_delivery_above' => $unifiedSetting->free_delivery_above,
            'max_delivery_distance' => $unifiedSetting->max_delivery_distance,
        ];
    }

    /**
     * Check if profile has custom overrides
     */
    public function hasCustomOverrides(): bool
    {
        return !is_null($this->custom_admin_commission_rate) ||
               !is_null($this->custom_driver_commission_rate) ||
               !is_null($this->custom_delivery_base_charge);
    }

    /**
     * Get profile status with details
     */
    public function getStatusDetails(): array
    {
        $now = now();
        
        if (!$this->is_active) {
            return [
                'status' => 'inactive',
                'message' => 'الملف الشخصي غير مفعل',
                'color' => 'danger'
            ];
        }
        
        if ($this->expires_at && $this->expires_at->isPast()) {
            return [
                'status' => 'expired',
                'message' => 'انتهت صلاحية الملف الشخصي',
                'color' => 'warning'
            ];
        }
        
        if ($this->expires_at && $this->expires_at->diffInDays($now) <= 7) {
            return [
                'status' => 'expiring_soon',
                'message' => 'ينتهي خلال ' . $this->expires_at->diffInDays($now) . ' أيام',
                'color' => 'warning'
            ];
        }
        
        return [
            'status' => 'active',
            'message' => 'نشط',
            'color' => 'success'
        ];
    }

    /**
     * Create a custom profile for a restaurant
     */
    public static function createCustomProfile(
        string $restaurantId,
        string $unifiedPricingSettingId,
        array $customRates = [],
        array $metadata = []
    ): self {
        return self::create([
            'restaurant_id' => $restaurantId,
            'unified_pricing_setting_id' => $unifiedPricingSettingId,
            'profile_name' => $metadata['profile_name'] ?? 'ملف مخصص',
            'description' => $metadata['description'] ?? 'ملف تسعير مخصص',
            'profile_type' => 'custom',
            'custom_admin_commission_rate' => $customRates['admin_commission_rate'] ?? null,
            'custom_driver_commission_rate' => $customRates['driver_commission_rate'] ?? null,
            'custom_delivery_base_charge' => $customRates['delivery_base_charge'] ?? null,
            'is_active' => true,
            'activated_at' => now(),
            'created_by' => auth()->id(),
        ]);
    }

    /**
     * Get the active profile for a restaurant
     */
    public static function getActiveProfileForRestaurant(string $restaurantId): ?self
    {
        return self::where('restaurant_id', $restaurantId)
            ->where('is_active', true)
            ->where(function ($query) {
                $query->whereNull('expires_at')
                      ->orWhere('expires_at', '>', now());
            })
            ->with('unifiedPricingSetting')
            ->first();
    }

    /**
     * Calculate pricing for an order using this profile
     */
    public function calculateOrderPricing(array $orderData): array
    {
        $effectiveRates = $this->getEffectiveCommissionRates();
        $deliverySettings = $this->getEffectiveDeliverySettings();
        
        $orderTotal = $orderData['order_total'] ?? 0;
        $distance = $orderData['distance_km'] ?? 0;
        
        // Calculate delivery charge
        $deliveryCharge = $this->calculateDeliveryCharge($deliverySettings, $distance, $orderData);
        
        // Calculate commissions
        $adminCommission = ($orderTotal * $effectiveRates['admin_commission_rate']) / 100;
        $driverCommission = $this->calculateDriverCommission($effectiveRates, $distance, $orderData);
        
        return [
            'order_total' => $orderTotal,
            'delivery_charge' => $deliveryCharge,
            'admin_commission' => round($adminCommission, 2),
            'driver_commission' => round($driverCommission, 2),
            'restaurant_earnings' => round($orderTotal - $adminCommission, 2),
            'total_customer_charge' => round($orderTotal + $deliveryCharge, 2),
            'profile_used' => $this->profile_name,
            'calculation_details' => [
                'admin_commission_rate' => $effectiveRates['admin_commission_rate'],
                'driver_commission_rate' => $effectiveRates['driver_commission_rate'],
                'delivery_settings' => $deliverySettings,
                'has_custom_overrides' => $this->hasCustomOverrides(),
            ]
        ];
    }

    /**
     * Calculate delivery charge based on settings
     */
    private function calculateDeliveryCharge(array $settings, float $distance, array $orderData): float
    {
        $charge = 0;
        
        switch ($settings['delivery_charge_type']) {
            case 'fixed':
                $charge = $settings['base_delivery_charge'];
                break;
                
            case 'per_km':
                $charge = $distance * $settings['per_km_delivery_rate'];
                break;
                
            case 'dynamic':
                $charge = $settings['base_delivery_charge'] + ($distance * $settings['per_km_delivery_rate']);
                break;
        }
        
        // Apply min/max limits
        $charge = max($settings['min_delivery_charge'], min($charge, $settings['max_delivery_charge']));
        
        // Check for free delivery
        if (isset($orderData['order_total']) && $orderData['order_total'] >= $settings['free_delivery_above']) {
            $charge = 0;
        }
        
        return round($charge, 2);
    }

    /**
     * Calculate driver commission
     */
    private function calculateDriverCommission(array $rates, float $distance, array $orderData): float
    {
        $unifiedSetting = $this->unifiedPricingSetting;
        
        // Base commission
        $commission = ($rates['driver_commission_rate'] * ($orderData['order_total'] ?? 0)) / 100;
        
        // Add distance-based commission if applicable
        if ($distance > 0 && $unifiedSetting->driver_distance_rate) {
            $commission += $distance * $unifiedSetting->driver_distance_rate;
        }
        
        // Apply min/max limits
        if ($unifiedSetting->min_driver_commission) {
            $commission = max($commission, $unifiedSetting->min_driver_commission);
        }
        
        if ($unifiedSetting->max_driver_commission) {
            $commission = min($commission, $unifiedSetting->max_driver_commission);
        }
        
        return $commission;
    }

    /**
     * Get the linked commission profile for this restaurant
     */
    public function getLinkedCommissionProfile(): ?CommissionProfile
    {
        // Get the commission profile through restaurant pricing integration
        $integration = RestaurantPricingIntegration::where('restaurant_pricing_profile_id', $this->id)
            ->where('integration_status', 'active')
            ->first();

        if ($integration && $integration->commission_profile_id) {
            return CommissionProfile::find($integration->commission_profile_id);
        }

        return null;
    }
}
