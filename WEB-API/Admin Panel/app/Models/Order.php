<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class Order extends Model
{
    use HasFactory;

    protected $connection = 'pgsql'; // Use PostgreSQL connection for Firebase data
    protected $table = 'orders';
    protected $keyType = 'string';
    public $incrementing = false;

    // Payment Status Constants
    const PAYMENT_PENDING = 'pending';
    const PAYMENT_COMPLETED = 'completed';
    const PAYMENT_FAILED = 'failed';
    const PAYMENT_REFUNDED = 'refunded';

    protected $fillable = [
        'id', 'customerId', 'vendorId', 'driverId', 'status', 'products', 'subTotal',
        'discount', 'discountType', 'couponCode', 'deliveryCharge', 'tipValue',
        'tax', 'taxSetting', 'total', 'address', 'author', 'vendor', 'driver',
        'paymentMethod', 'paymentStatus', 'orderType', 'notes', 'estimatedTimeToPrepare', 'createdAt',
        'admin_commission', 'commission_rate', 'commission_type', 'commission_calculated'
    ];

    protected $casts = [
        'products' => 'array',
        'subTotal' => 'decimal:2',
        'discount' => 'decimal:2',
        'deliveryCharge' => 'decimal:2',
        'tipValue' => 'decimal:2',
        'tax' => 'decimal:2',
        'total' => 'decimal:2',
        'taxSetting' => 'array',
        'address' => 'array',
        'author' => 'array',
        'vendor' => 'array',
        'driver' => 'array',
        'paymentStatus' => 'boolean',
        'estimatedTimeToPrepare' => 'datetime',
        'createdAt' => 'datetime',
        'admin_commission' => 'decimal:2',
        'commission_rate' => 'decimal:2',
        'commission_calculated' => 'boolean'
    ];

    /**
     * Relationships - استخدام UnifiedUser مع البنية الجديدة
     */
    public function customer()
    {
        return $this->belongsTo(UnifiedUser::class, 'customerId', 'id')
                    ->where('role', 'customer');
    }

    public function vendor()
    {
        return $this->belongsTo(UnifiedUser::class, 'vendorId', 'id')
                    ->where('role', 'vendor');
    }

    public function driver()
    {
        return $this->belongsTo(UnifiedUser::class, 'driverId', 'id')
                    ->where('role', 'driver');
    }

    /**
     * Get customer attribute with proper loading
     */
    public function getCustomerAttribute()
    {
        if (!$this->relationLoaded('customer')) {
            $this->load('customer');
        }
        return $this->getRelation('customer');
    }

    /**
     * Get vendor attribute with proper loading
     */
    public function getVendorAttribute()
    {
        if (!$this->relationLoaded('vendor')) {
            $this->load('vendor');
        }
        return $this->getRelation('vendor');
    }

    /**
     * Get driver attribute with proper loading
     */
    public function getDriverAttribute()
    {
        if (!$this->relationLoaded('driver')) {
            $this->load('driver');
        }
        return $this->getRelation('driver');
    }

    /**
     * علاقات بديلة مع AppUser (للمرونة)
     */
    /**
     * Relationships with profiles - العلاقات مع الملفات الشخصية
     */
    public function customerProfile()
    {
        return $this->hasOneThrough(
            CustomerProfile::class,
            UnifiedUser::class,
            'id', // Foreign key on users table
            'user_id', // Foreign key on customer_profiles table
            'authorId', // Local key on orders table
            'id' // Local key on users table
        );
    }

    public function vendorProfile()
    {
        return $this->hasOneThrough(
            VendorProfile::class,
            UnifiedUser::class,
            'id', // Foreign key on users table
            'user_id', // Foreign key on vendor_profiles table
            'vendorId', // Local key on orders table
            'id' // Local key on users table
        );
    }

    public function driverProfile()
    {
        return $this->hasOneThrough(
            DriverProfile::class,
            UnifiedUser::class,
            'id', // Foreign key on users table
            'user_id', // Foreign key on driver_profiles table
            'driverId', // Local key on orders table
            'id' // Local key on users table
        );
    }

    public function orderItems()
    {
        return $this->hasMany(OrderItem::class, 'orderID');
    }

    public function payments()
    {
        return $this->hasMany(Payment::class, 'orderID');
    }

    public function reviews()
    {
        return $this->hasMany(Review::class, 'orderID');
    }

    public function statusHistory()
    {
        return $this->hasMany(OrderStatusHistory::class, 'orderID');
    }

    public function notifications()
    {
        return $this->hasMany(OrderNotification::class, 'orderID');
    }

    public function coupon()
    {
        return $this->belongsTo(Coupon::class, 'couponCode', 'code');
    }

    /**
     * Query Scopes
     */
    public function scopeByStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    public function scopeConfirmed(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_CONFIRMED);
    }

    public function scopePreparing(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_PREPARING);
    }

    public function scopeReady(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_READY);
    }

    public function scopeInProgress(Builder $query): Builder
    {
        return $query->whereIn('status', [
            self::STATUS_CONFIRMED,
            self::STATUS_PREPARING,
            self::STATUS_READY,
            self::STATUS_PICKED_UP,
            self::STATUS_ON_THE_WAY
        ]);
    }

    public function scopeCompleted(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_DELIVERED);
    }

    public function scopeCancelled(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_CANCELLED);
    }

    public function scopeByCustomer(Builder $query, string $customerId): Builder
    {
        return $query->where('authorId', $customerId);
    }

    public function scopeByVendor(Builder $query, string $vendorId): Builder
    {
        return $query->where('vendorId', $vendorId);
    }

    public function scopeByDriver(Builder $query, string $driverId): Builder
    {
        return $query->where('driverId', $driverId);
    }

    public function scopeByOrderType(Builder $query, string $type): Builder
    {
        return $query->where('orderType', $type);
    }

    public function scopeDelivery(Builder $query): Builder
    {
        return $query->where('orderType', self::TYPE_DELIVERY);
    }

    public function scopePickup(Builder $query): Builder
    {
        return $query->where('orderType', self::TYPE_PICKUP);
    }

    public function scopeDineIn(Builder $query): Builder
    {
        return $query->where('orderType', self::TYPE_DINE_IN);
    }

    public function scopeScheduled(Builder $query): Builder
    {
        return $query->where('isScheduled', true);
    }

    public function scopeToday(Builder $query): Builder
    {
        return $query->whereDate('createdAt', today());
    }

    public function scopeThisWeek(Builder $query): Builder
    {
        return $query->whereBetween('createdAt', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    public function scopeThisMonth(Builder $query): Builder
    {
        return $query->whereMonth('createdAt', now()->month)
                    ->whereYear('createdAt', now()->year);
    }

    public function scopeByDateRange(Builder $query, $startDate, $endDate): Builder
    {
        return $query->whereBetween('createdAt', [$startDate, $endDate]);
    }

    public function scopeHighValue(Builder $query, float $minAmount = 100): Builder
    {
        return $query->where('total', '>=', $minAmount);
    }

    public function scopePaid(Builder $query): Builder
    {
        return $query->where('paymentStatus', self::PAYMENT_PAID);
    }

    public function scopePendingPayment(Builder $query): Builder
    {
        return $query->where('paymentStatus', self::PAYMENT_PENDING);
    }

    /**
     * Accessors & Mutators
     */
    public function getStatusDisplayAttribute(): string
    {
        $statusLabels = [
            self::STATUS_PENDING => 'في الانتظار',
            self::STATUS_CONFIRMED => 'مؤكد',
            self::STATUS_PREPARING => 'قيد التحضير',
            self::STATUS_READY => 'جاهز',
            self::STATUS_PICKED_UP => 'تم الاستلام',
            self::STATUS_ON_THE_WAY => 'في الطريق',
            self::STATUS_DELIVERED => 'تم التوصيل',
            self::STATUS_CANCELLED => 'ملغي',
            self::STATUS_REFUNDED => 'مسترد'
        ];

        return $statusLabels[$this->status] ?? $this->status;
    }

    public function getOrderTypeDisplayAttribute(): string
    {
        $typeLabels = [
            self::TYPE_DELIVERY => 'توصيل',
            self::TYPE_PICKUP => 'استلام',
            self::TYPE_DINE_IN => 'تناول في المطعم'
        ];

        return $typeLabels[$this->orderType] ?? $this->orderType;
    }

    public function getPaymentStatusDisplayAttribute(): string
    {
        $statusLabels = [
            self::PAYMENT_PENDING => 'في الانتظار',
            self::PAYMENT_PAID => 'مدفوع',
            self::PAYMENT_FAILED => 'فشل',
            self::PAYMENT_REFUNDED => 'مسترد'
        ];

        return $statusLabels[$this->paymentStatus] ?? $this->paymentStatus;
    }

    public function getFormattedOrderNumberAttribute(): string
    {
        return $this->orderNumber ? '#' . $this->orderNumber : '#' . substr($this->id, 0, 8);
    }

    public function getTotalItemsAttribute(): int
    {
        return $this->orderItems()->sum('quantity');
    }

    public function getNetAmountAttribute(): float
    {
        return $this->total - $this->deliveryCharge - $this->tipValue - $this->tax;
    }

    public function getIsCompletedAttribute(): bool
    {
        return $this->status === self::STATUS_DELIVERED;
    }

    public function getIsCancelledAttribute(): bool
    {
        return $this->status === self::STATUS_CANCELLED;
    }

    public function getIsRefundableAttribute(): bool
    {
        return in_array($this->status, [
            self::STATUS_PENDING,
            self::STATUS_CONFIRMED,
            self::STATUS_PREPARING
        ]) && $this->paymentStatus === self::PAYMENT_PAID;
    }

    public function getCanBeCancelledAttribute(): bool
    {
        return in_array($this->status, [
            self::STATUS_PENDING,
            self::STATUS_CONFIRMED
        ]);
    }

    public function getEstimatedDeliveryTimeDisplayAttribute(): ?string
    {
        if (!$this->estimatedDeliveryTime) {
            return null;
        }

        return $this->estimatedDeliveryTime->format('H:i');
    }

    public function getDeliveryTimeInMinutesAttribute(): ?int
    {
        if (!$this->deliveredAt || !$this->confirmedAt) {
            return null;
        }

        return $this->confirmedAt->diffInMinutes($this->deliveredAt);
    }

    public function getPreparationTimeInMinutesAttribute(): ?int
    {
        if (!$this->readyAt || !$this->preparingAt) {
            return null;
        }

        return $this->preparingAt->diffInMinutes($this->readyAt);
    }

    /**
     * Order Lifecycle Management
     */
    public function confirm(): bool
    {
        if ($this->status !== self::STATUS_PENDING) {
            return false;
        }

        $this->update([
            'status' => self::STATUS_CONFIRMED,
            'confirmedAt' => now()
        ]);

        $this->logStatusChange(self::STATUS_CONFIRMED);
        $this->triggerNotification('order_confirmed');

        // Update vendor order stats
        $this->vendor->increment('totalOrders');

        return true;
    }

    public function startPreparing(): bool
    {
        if ($this->status !== self::STATUS_CONFIRMED) {
            return false;
        }

        $this->update([
            'status' => self::STATUS_PREPARING,
            'preparingAt' => now()
        ]);

        $this->logStatusChange(self::STATUS_PREPARING);
        $this->triggerNotification('order_preparing');

        return true;
    }

    public function markReady(): bool
    {
        if ($this->status !== self::STATUS_PREPARING) {
            return false;
        }

        $actualPreparationTime = $this->preparingAt ?
            $this->preparingAt->diffInMinutes(now()) : null;

        $this->update([
            'status' => self::STATUS_READY,
            'readyAt' => now(),
            'actualPreparationTime' => $actualPreparationTime
        ]);

        $this->logStatusChange(self::STATUS_READY);
        $this->triggerNotification('order_ready');

        // Notify driver if assigned
        if ($this->driverID) {
            $this->triggerNotification('order_ready_for_pickup');
        }

        return true;
    }

    public function markPickedUp(): bool
    {
        if ($this->status !== self::STATUS_READY) {
            return false;
        }

        $this->update([
            'status' => self::STATUS_PICKED_UP,
            'pickedUpAt' => now()
        ]);

        $this->logStatusChange(self::STATUS_PICKED_UP);
        $this->triggerNotification('order_picked_up');

        return true;
    }

    public function markOnTheWay(): bool
    {
        if ($this->status !== self::STATUS_PICKED_UP) {
            return false;
        }

        $this->update([
            'status' => self::STATUS_ON_THE_WAY
        ]);

        $this->logStatusChange(self::STATUS_ON_THE_WAY);
        $this->triggerNotification('order_on_the_way');

        return true;
    }

    public function markDelivered(): bool
    {
        if (!in_array($this->status, [self::STATUS_ON_THE_WAY, self::STATUS_READY])) {
            return false;
        }

        $deliveryTime = $this->pickedUpAt ?
            $this->pickedUpAt->diffInMinutes(now()) : null;

        $this->update([
            'status' => self::STATUS_DELIVERED,
            'deliveredAt' => now(),
            'deliveryTime' => $deliveryTime
        ]);

        $this->logStatusChange(self::STATUS_DELIVERED);
        $this->triggerNotification('order_delivered');

        // Process driver commission through unified pricing system
        $this->processDriverCommission();

        // Update customer and driver stats
        $this->customer->increment('totalOrders');
        $this->customer->increment('totalSpent', $this->total);
        $this->customer->update(['lastOrderAt' => now()]);

        if ($this->driver) {
            $this->driver->increment('totalDeliveries');
            $this->driver->update(['lastDeliveryAt' => now()]);
            $this->driver->completeOrder();
        }

        // Award loyalty points
        $this->awardLoyaltyPoints();

        return true;
    }

    /**
     * Process driver commission through unified pricing system
     */
    private function processDriverCommission(): void
    {
        try {
            if (!$this->driverID) {
                return; // No driver assigned
            }

            $commissionService = app(\App\Services\UnifiedDriverCommissionService::class);
            $result = $commissionService->processOrderCommission($this);

            if ($result['success']) {
                Log::info('Driver commission processed successfully', [
                    'order_id' => $this->id,
                    'driver_id' => $this->driverID,
                    'commission_amount' => $result['commission_amount']
                ]);
            } else {
                Log::error('Failed to process driver commission', [
                    'order_id' => $this->id,
                    'driver_id' => $this->driverID,
                    'error' => $result['error']
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Exception while processing driver commission', [
                'order_id' => $this->id,
                'driver_id' => $this->driverID,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function cancel(string $reason = null): bool
    {
        if (!$this->canBeCancelled) {
            return false;
        }

        $this->update([
            'status' => self::STATUS_CANCELLED,
            'cancelledAt' => now(),
            'cancellationReason' => $reason
        ]);

        $this->logStatusChange(self::STATUS_CANCELLED, $reason);
        $this->triggerNotification('order_cancelled');

        // Process refund if payment was made
        if ($this->paymentStatus === self::PAYMENT_PAID) {
            $this->processRefund();
        }

        // Free up driver if assigned
        if ($this->driver) {
            $this->driver->update([
                'currentOrderId' => null,
                'isAvailable' => true
            ]);
        }

        return true;
    }

    /**
     * Payment Integration Hooks
     */
    public function processPayment(array $paymentData): bool
    {
        try {
            // Create payment record
            $payment = $this->payments()->create([
                'id' => \Str::uuid(),
                'amount' => $this->total,
                'method' => $paymentData['method'],
                'status' => 'processing',
                'transactionId' => $paymentData['transaction_id'] ?? null,
                'gatewayResponse' => $paymentData,
                'createdAt' => now()
            ]);

            // Process payment through gateway
            $result = $this->processPaymentGateway($paymentData);

            if ($result['success']) {
                $this->update(['paymentStatus' => self::PAYMENT_PAID]);
                $payment->update([
                    'status' => 'completed',
                    'paidAt' => now()
                ]);

                $this->triggerNotification('payment_successful');
                return true;
            } else {
                $this->update(['paymentStatus' => self::PAYMENT_FAILED]);
                $payment->update(['status' => 'failed']);

                $this->triggerNotification('payment_failed');
                return false;
            }
        } catch (\Exception $e) {
            \Log::error('Payment processing failed for order ' . $this->id, [
                'error' => $e->getMessage(),
                'order_id' => $this->id
            ]);

            return false;
        }
    }

    public function processRefund(float $amount = null): bool
    {
        $refundAmount = $amount ?? $this->total;

        try {
            // Process refund through payment gateway
            $result = $this->processRefundGateway($refundAmount);

            if ($result['success']) {
                $this->update([
                    'paymentStatus' => self::PAYMENT_REFUNDED,
                    'refundAmount' => $refundAmount,
                    'refundedAt' => now()
                ]);

                // Add refund to customer wallet if partial refund
                if ($refundAmount < $this->total) {
                    $this->customer->addToWallet(
                        $refundAmount,
                        'Order refund for #' . $this->formattedOrderNumber
                    );
                }

                $this->triggerNotification('refund_processed');
                return true;
            }

            return false;
        } catch (\Exception $e) {
            \Log::error('Refund processing failed for order ' . $this->id, [
                'error' => $e->getMessage(),
                'order_id' => $this->id,
                'amount' => $refundAmount
            ]);

            return false;
        }
    }

    /**
     * Notification Triggers
     */
    public function triggerNotification(string $type, array $data = []): void
    {
        $notificationData = array_merge([
            'order_id' => $this->id,
            'order_number' => $this->formattedOrderNumber,
            'customer_id' => $this->authorID,
            'vendor_id' => $this->vendorID,
            'driver_id' => $this->driverID,
            'status' => $this->status,
            'type' => $type,
            'timestamp' => now()
        ], $data);

        // Store notification
        $this->notifications()->create([
            'id' => \Str::uuid(),
            'type' => $type,
            'data' => $notificationData,
            'createdAt' => now()
        ]);

        // Trigger real-time notifications
        Event::dispatch('order.notification', [$this, $type, $notificationData]);
    }

    /**
     * Helper Methods
     */
    private function logStatusChange(string $newStatus, string $notes = null): void
    {
        $this->statusHistory()->create([
            'id' => \Str::uuid(),
            'fromStatus' => $this->getOriginal('status'),
            'toStatus' => $newStatus,
            'notes' => $notes,
            'changedBy' => auth()->id(),
            'changedAt' => now()
        ]);
    }

    private function processPaymentGateway(array $paymentData): array
    {
        // This would integrate with actual payment gateways
        // For now, return success for testing
        return ['success' => true, 'transaction_id' => \Str::uuid()];
    }

    private function processRefundGateway(float $amount): array
    {
        // This would integrate with actual payment gateways
        // For now, return success for testing
        return ['success' => true, 'refund_id' => \Str::uuid()];
    }

    private function awardLoyaltyPoints(): void
    {
        $pointsToAward = floor($this->total / 10); // 1 point per 10 currency units

        if ($pointsToAward > 0) {
            $this->customer->increment('loyaltyPoints', $pointsToAward);
            $this->update(['loyaltyPointsEarned' => $pointsToAward]);
        }
    }

    /**
     * Cache Methods
     */
    public function getCachedOrderItems()
    {
        return Cache::remember(
            "order_items_{$this->id}",
            1800, // 30 minutes
            fn() => $this->orderItems()->with(['food', 'addOns'])->get()
        );
    }

    /**
     * Model Events
     */
    protected static function booted(): void
    {
        static::creating(function (Order $order) {
            if (!$order->id) {
                $order->id = \Str::uuid();
            }

            // Generate order number
            if (!$order->orderNumber) {
                $order->orderNumber = 'ORD-' . date('Ymd') . '-' . str_pad(
                    static::whereDate('created_at', today())->count() + 1,
                    4,
                    '0',
                    STR_PAD_LEFT
                );
            }

            // Set default status
            if (!$order->status) {
                $order->status = self::STATUS_PENDING;
            }

            // Set default payment status
            if (!$order->paymentStatus) {
                $order->paymentStatus = self::PAYMENT_PENDING;
            }
        });

        static::created(function (Order $order) {
            $order->logStatusChange($order->status);
            $order->triggerNotification('order_created');
        });

        static::updated(function (Order $order) {
            // Clear cache when order is updated
            \Illuminate\Support\Facades\Cache::forget("order_items_{$order->id}");
        });
    }

    /**
     * Validation Rules
     */
    public static function validationRules(): array
    {
        return [
            'customerId' => 'required|exists:users,id',
            'vendorId' => 'required|exists:vendors,id',
            'driverId' => 'nullable|exists:drivers,id',
            'orderType' => 'required|in:delivery,pickup,dine_in',
            'subTotal' => 'required|numeric|min:0',
            'total' => 'required|numeric|min:0',
            'paymentMethod' => 'required|string|max:50',
            'address' => 'required_if:orderType,delivery|array',
            'scheduledAt' => 'nullable|date|after:now',
            'specialInstructions' => 'nullable|string|max:500',
            'deliveryInstructions' => 'nullable|string|max:500'
        ];
    }

    /**
     * العلاقة مع سجل العمولة
     */
    public function commission()
    {
        return $this->hasOne(OrderCommission::class, 'order_id');
    }

    /**
     * حساب العمولة للطلب
     */
    public function calculateCommission()
    {
        $commissionService = app(\App\Services\CommissionService::class);
        return $commissionService->calculateAndSaveCommission($this);
    }

    /**
     * التحقق من حساب العمولة
     */
    public function hasCommissionCalculated(): bool
    {
        return $this->commission_calculated ?? false;
    }

    /**
     * الحصول على مبلغ العمولة
     */
    public function getCommissionAmount(): float
    {
        return $this->admin_commission ?? 0.0;
    }

    /**
     * الحصول على نسبة العمولة
     */
    public function getCommissionRate(): float
    {
        return $this->commission_rate ?? 0.0;
    }
}
