<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\UnifiedPricingSettingsService;
use App\Services\SettingsSynchronizationService;
use App\Models\UnifiedPricingSetting;
use App\Models\RestaurantPricingProfile;
use App\Models\RestaurantPricingIntegration;
use App\Models\PricingCalculationHistory;
use App\Models\CommissionProfile;
use App\Models\UnifiedUser;
use App\Models\AppUser;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

/**
 * Unified Pricing Controller
 * Manages integrated pricing and commission settings
 */
class UnifiedPricingController extends Controller
{
    private UnifiedPricingSettingsService $pricingService;
    private SettingsSynchronizationService $syncService;

    public function __construct(
        UnifiedPricingSettingsService $pricingService,
        SettingsSynchronizationService $syncService
    ) {
        $this->pricingService = $pricingService;
        $this->syncService = $syncService;
    }

    /**
     * Get all unified pricing settings
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = UnifiedPricingSetting::query();

            // Apply filters
            if ($request->has('scope')) {
                $query->where('scope', $request->scope);
            }

            if ($request->has('is_active')) {
                $query->where('is_active', $request->boolean('is_active'));
            }

            if ($request->has('effective_date')) {
                $query->effective($request->effective_date);
            }

            $settings = $query->orderBy('priority', 'desc')
                ->orderBy('created_at', 'desc')
                ->paginate($request->get('per_page', 15));

            return response()->json([
                'success' => true,
                'message' => 'تم جلب إعدادات التسعير الموحد بنجاح',
                'data' => $settings
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to fetch unified pricing settings', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => 'فشل في جلب إعدادات التسعير الموحد',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create new unified pricing setting
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:unified_pricing_settings,name',
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'scope' => 'required|in:global,zone,vendor,driver',
            'scope_id' => 'nullable|uuid',
            'distance_unit' => 'required|in:km,mile',
            'max_delivery_distance' => 'required|numeric|min:1|max:100',
            'driver_search_radius' => 'required|numeric|min:1|max:50',
            'delivery_charge_type' => 'required|in:fixed,per_km,dynamic,zone_based',
            'base_delivery_charge' => 'required|numeric|min:0',
            'per_km_delivery_rate' => 'required|numeric|min:0',
            'min_delivery_charge' => 'required|numeric|min:0',
            'max_delivery_charge' => 'required|numeric|min:0',
            'free_delivery_above' => 'nullable|numeric|min:0',
            'commission_calculation_method' => 'required|in:percentage,fixed,hybrid,distance_based',
            'admin_commission_rate' => 'nullable|numeric|min:0|max:100',
            'driver_commission_rate' => 'nullable|numeric|min:0',
            'driver_distance_rate' => 'nullable|numeric|min:0',
            'min_driver_commission' => 'nullable|numeric|min:0',
            'max_driver_commission' => 'nullable|numeric|min:0',
            'urgent_delivery_multiplier' => 'nullable|numeric|min:1|max:5',
            'time_based_multipliers' => 'nullable|array',
            'day_based_multipliers' => 'nullable|array',
            'zone_specific_rates' => 'nullable|array',
            'is_active' => 'boolean',
            'priority' => 'integer|min:0|max:100',
            'effective_from' => 'nullable|date',
            'effective_until' => 'nullable|date|after:effective_from',
            'auto_sync_enabled' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $data = $validator->validated();
            $data['created_by'] = auth()->id();

            $setting = $this->pricingService->createUnifiedSetting($data);

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء إعدادات التسعير الموحد بنجاح',
                'data' => $setting
            ], 201);

        } catch (\Exception $e) {
            Log::error('Failed to create unified pricing setting', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في إنشاء إعدادات التسعير الموحد',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get specific unified pricing setting
     */
    public function show(string $id): JsonResponse
    {
        try {
            $setting = UnifiedPricingSetting::with(['calculationHistory', 'commissionRules', 'deliveryChargeSettings'])
                ->findOrFail($id);

            return response()->json([
                'success' => true,
                'message' => 'تم جلب إعدادات التسعير الموحد بنجاح',
                'data' => $setting
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'إعدادات التسعير الموحد غير موجودة',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update unified pricing setting
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255|unique:unified_pricing_settings,name,' . $id,
            'display_name' => 'sometimes|string|max:255',
            'description' => 'nullable|string',
            'distance_unit' => 'sometimes|in:km,mile',
            'max_delivery_distance' => 'sometimes|numeric|min:1|max:100',
            'driver_search_radius' => 'sometimes|numeric|min:1|max:50',
            'delivery_charge_type' => 'sometimes|in:fixed,per_km,dynamic,zone_based',
            'base_delivery_charge' => 'sometimes|numeric|min:0',
            'per_km_delivery_rate' => 'sometimes|numeric|min:0',
            'min_delivery_charge' => 'sometimes|numeric|min:0',
            'max_delivery_charge' => 'sometimes|numeric|min:0',
            'free_delivery_above' => 'nullable|numeric|min:0',
            'commission_calculation_method' => 'sometimes|in:percentage,fixed,hybrid,distance_based',
            'admin_commission_rate' => 'nullable|numeric|min:0|max:100',
            'driver_commission_rate' => 'nullable|numeric|min:0',
            'driver_distance_rate' => 'nullable|numeric|min:0',
            'min_driver_commission' => 'nullable|numeric|min:0',
            'max_driver_commission' => 'nullable|numeric|min:0',
            'urgent_delivery_multiplier' => 'nullable|numeric|min:1|max:5',
            'time_based_multipliers' => 'nullable|array',
            'day_based_multipliers' => 'nullable|array',
            'zone_specific_rates' => 'nullable|array',
            'is_active' => 'boolean',
            'priority' => 'integer|min:0|max:100',
            'effective_from' => 'nullable|date',
            'effective_until' => 'nullable|date|after:effective_from',
            'auto_sync_enabled' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $setting = UnifiedPricingSetting::findOrFail($id);
            $data = $validator->validated();
            $data['updated_by'] = auth()->id();

            $setting->update($data);

            // Sync with related settings if auto sync is enabled
            if ($setting->auto_sync_enabled) {
                $this->syncService->syncUnifiedSetting($setting);
            }

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث إعدادات التسعير الموحد بنجاح',
                'data' => $setting->fresh()
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update unified pricing setting', [
                'id' => $id,
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في تحديث إعدادات التسعير الموحد',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete unified pricing setting
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $setting = UnifiedPricingSetting::findOrFail($id);
            $setting->delete();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف إعدادات التسعير الموحد بنجاح'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to delete unified pricing setting', [
                'id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في حذف إعدادات التسعير الموحد',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate pricing for an order
     */
    public function calculatePricing(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'order_total' => 'required|numeric|min:0',
            'origin_lat' => 'required|numeric|between:-90,90',
            'origin_lng' => 'required|numeric|between:-180,180',
            'dest_lat' => 'required|numeric|between:-90,90',
            'dest_lng' => 'required|numeric|between:-180,180',
            'delivery_type' => 'sometimes|in:normal,urgent,scheduled',
            'scope' => 'sometimes|in:global,zone,vendor,driver',
            'scope_id' => 'nullable|uuid',
            'zone_id' => 'nullable|uuid',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $orderData = $validator->validated();
            $result = $this->pricingService->calculateOrderPricing($orderData);

            return response()->json([
                'success' => true,
                'message' => 'تم حساب التسعير بنجاح',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to calculate pricing', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في حساب التسعير',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Synchronize all settings
     */
    public function syncAllSettings(): JsonResponse
    {
        try {
            $result = $this->syncService->syncAllSettings();

            return response()->json([
                'success' => $result['success'],
                'message' => $result['message'],
                'data' => $result
            ], $result['success'] ? 200 : 500);

        } catch (\Exception $e) {
            Log::error('Failed to sync all settings', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في مزامنة الإعدادات',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Synchronize specific setting
     */
    public function syncSetting(string $id): JsonResponse
    {
        try {
            $setting = UnifiedPricingSetting::findOrFail($id);
            $result = $this->syncService->syncUnifiedSetting($setting);

            return response()->json([
                'success' => $result['success'],
                'message' => $result['success'] ? 'تم مزامنة الإعداد بنجاح' : 'فشل في مزامنة الإعداد',
                'data' => $result
            ], $result['success'] ? 200 : 500);

        } catch (\Exception $e) {
            Log::error('Failed to sync setting', ['id' => $id, 'error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في مزامنة الإعداد',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate settings consistency
     */
    public function validateConsistency(): JsonResponse
    {
        try {
            $result = $this->syncService->validateSettingsConsistency();

            return response()->json([
                'success' => $result['success'],
                'message' => $result['success'] ? 'تم التحقق من تطابق الإعدادات' : 'فشل في التحقق من تطابق الإعدادات',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to validate consistency', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في التحقق من تطابق الإعدادات',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get calculation analytics
     */
    public function getAnalytics(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after:start_date',
            'type' => 'sometimes|in:efficiency,breakdown,distance,hourly'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $startDate = $request->start_date;
            $endDate = $request->end_date;
            $type = $request->get('type', 'efficiency');

            $data = match ($type) {
                'efficiency' => PricingCalculationHistory::getEfficiencyMetrics($startDate, $endDate),
                'breakdown' => PricingCalculationHistory::getCalculationBreakdownByMethod($startDate, $endDate),
                'distance' => PricingCalculationHistory::getDistanceBasedAnalysis($startDate, $endDate),
                'hourly' => PricingCalculationHistory::getHourlyPatterns($startDate, $endDate),
                default => PricingCalculationHistory::getEfficiencyMetrics($startDate, $endDate)
            };

            return response()->json([
                'success' => true,
                'message' => 'تم جلب تحليلات التسعير بنجاح',
                'data' => $data
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get analytics', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في جلب تحليلات التسعير',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get applicable setting for given parameters
     */
    public function getApplicableSetting(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'scope' => 'sometimes|in:global,zone,vendor,driver',
            'scope_id' => 'nullable|uuid',
            'zone_id' => 'nullable|uuid',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $scope = $request->get('scope', 'global');
            $scopeId = $request->get('scope_id');
            $zoneId = $request->get('zone_id');

            $setting = UnifiedPricingSetting::getApplicableSetting($scope, $scopeId, $zoneId);

            if (!$setting) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا توجد إعدادات تسعير مطبقة للمعايير المحددة'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'تم جلب الإعدادات المطبقة بنجاح',
                'data' => $setting
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get applicable setting', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في جلب الإعدادات المطبقة',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all restaurants with their pricing profiles
     */
    public function getRestaurantsWithProfiles(): JsonResponse
    {
        try {
            $restaurants = $this->pricingService->getRestaurantsWithPricingProfiles();

            return response()->json([
                'success' => true,
                'message' => 'تم جلب المطاعم وملفات التسعير بنجاح',
                'data' => $restaurants
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get restaurants with profiles', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في جلب المطاعم وملفات التسعير',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create custom pricing profile for restaurant
     */
    public function createRestaurantProfile(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'restaurant_id' => 'required|uuid|exists:users,id',
            'profile_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'profile_type' => 'sometimes|in:standard,premium,custom',
            'custom_admin_commission_rate' => 'nullable|numeric|min:0|max:100',
            'custom_driver_commission_rate' => 'nullable|numeric|min:0|max:100',
            'custom_delivery_base_charge' => 'nullable|numeric|min:0',
            'admin_commission_rate' => 'nullable|numeric|min:0|max:100',
            'driver_commission_rate' => 'nullable|numeric|min:0|max:100',
            'base_delivery_charge' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $profile = $this->pricingService->createRestaurantPricingProfile(
                $request->restaurant_id,
                $request->all()
            );

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء ملف التسعير المخصص بنجاح',
                'data' => $profile->load('unifiedPricingSetting')
            ], 201);

        } catch (\Exception $e) {
            Log::error('Failed to create restaurant profile', [
                'restaurant_id' => $request->restaurant_id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في إنشاء ملف التسعير المخصص',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update restaurant pricing profile
     */
    public function updateRestaurantProfile(Request $request, string $profileId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'profile_name' => 'sometimes|string|max:255',
            'description' => 'nullable|string|max:1000',
            'custom_admin_commission_rate' => 'nullable|numeric|min:0|max:100',
            'custom_driver_commission_rate' => 'nullable|numeric|min:0|max:100',
            'custom_delivery_base_charge' => 'nullable|numeric|min:0',
            'is_active' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $profile = $this->pricingService->updateRestaurantPricingProfile(
                $profileId,
                $request->all()
            );

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث ملف التسعير بنجاح',
                'data' => $profile->load('unifiedPricingSetting')
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update restaurant profile', [
                'profile_id' => $profileId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في تحديث ملف التسعير',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get restaurant pricing profile details
     */
    public function getRestaurantProfile(string $restaurantId): JsonResponse
    {
        try {
            $profile = RestaurantPricingProfile::getActiveProfileForRestaurant($restaurantId);

            if (!$profile) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يوجد ملف تسعير نشط لهذا المطعم'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'تم جلب ملف التسعير بنجاح',
                'data' => [
                    'profile' => $profile,
                    'unified_setting' => $profile->unifiedPricingSetting,
                    'effective_rates' => $profile->getEffectiveCommissionRates(),
                    'delivery_settings' => $profile->getEffectiveDeliverySettings(),
                    'status' => $profile->getStatusDetails(),
                    'has_custom_overrides' => $profile->hasCustomOverrides(),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get restaurant profile', [
                'restaurant_id' => $restaurantId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في جلب ملف التسعير',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate pricing for specific restaurant
     */
    public function calculateRestaurantPricing(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'restaurant_id' => 'required|uuid',
            'order_total' => 'required|numeric|min:0',
            'distance_km' => 'nullable|numeric|min:0',
            'origin_lat' => 'nullable|numeric',
            'origin_lng' => 'nullable|numeric',
            'dest_lat' => 'nullable|numeric',
            'dest_lng' => 'nullable|numeric',
            'is_urgent' => 'sometimes|boolean',
            'order_time' => 'nullable|date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $result = $this->pricingService->calculateOrderPricing($request->all());

            return response()->json([
                'success' => true,
                'message' => 'تم حساب التسعير بنجاح',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to calculate restaurant pricing', [
                'restaurant_id' => $request->restaurant_id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في حساب التسعير',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new commission profile
     */
    public function createCommissionProfile(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|unique:commission_profiles,name',
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'profile_type' => 'required|in:standard,premium,custom',
            'admin_commission_rate' => 'required|numeric|min:0|max:100',
            'driver_commission_rate' => 'required|numeric|min:0|max:100',
            'base_delivery_charge' => 'required|numeric|min:0',
            'per_km_delivery_rate' => 'required|numeric|min:0',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $profile = CommissionProfile::create([
                'name' => $request->name,
                'display_name' => $request->display_name,
                'description' => $request->description,
                'profile_type' => $request->profile_type,
                'business_model' => 'percentage',
                'admin_commission_rate' => $request->admin_commission_rate,
                'driver_commission_rate' => $request->driver_commission_rate,
                'base_delivery_charge' => $request->base_delivery_charge,
                'per_km_delivery_rate' => $request->per_km_delivery_rate,
                'min_delivery_charge' => $request->base_delivery_charge,
                'max_delivery_charge' => $request->base_delivery_charge * 3,
                'is_active' => $request->boolean('is_active', true),
                'auto_assign_eligible' => true,
                'requires_approval' => false,
                'effective_from' => now(),
            ]);

            Log::info('Commission profile created', [
                'profile_id' => $profile->id,
                'name' => $profile->name,
                'created_by' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء ملف العمولة بنجاح',
                'data' => $profile
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to create commission profile', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في إنشاء ملف العمولة: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update commission profile
     */
    public function updateCommissionProfile(Request $request, string $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|unique:commission_profiles,name,' . $id,
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'profile_type' => 'required|in:standard,premium,custom',
            'admin_commission_rate' => 'required|numeric|min:0|max:100',
            'driver_commission_rate' => 'required|numeric|min:0|max:100',
            'base_delivery_charge' => 'required|numeric|min:0',
            'per_km_delivery_rate' => 'required|numeric|min:0',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $profile = CommissionProfile::findOrFail($id);

            $profile->update([
                'name' => $request->name,
                'display_name' => $request->display_name,
                'description' => $request->description,
                'profile_type' => $request->profile_type,
                'admin_commission_rate' => $request->admin_commission_rate,
                'driver_commission_rate' => $request->driver_commission_rate,
                'base_delivery_charge' => $request->base_delivery_charge,
                'per_km_delivery_rate' => $request->per_km_delivery_rate,
                'min_delivery_charge' => $request->base_delivery_charge,
                'max_delivery_charge' => $request->base_delivery_charge * 3,
                'is_active' => $request->boolean('is_active', true),
            ]);

            Log::info('Commission profile updated', [
                'profile_id' => $profile->id,
                'name' => $profile->name,
                'updated_by' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث ملف العمولة بنجاح',
                'data' => $profile
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update commission profile', [
                'profile_id' => $id,
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في تحديث ملف العمولة: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete commission profile
     */
    public function deleteCommissionProfile(string $id): JsonResponse
    {
        try {
            $profile = CommissionProfile::findOrFail($id);

            // Check if profile is being used by restaurants
            $restaurantsCount = $profile->activeRestaurants()->count();
            if ($restaurantsCount > 0) {
                return response()->json([
                    'success' => false,
                    'message' => "لا يمكن حذف الملف لأنه مستخدم من قبل {$restaurantsCount} مطعم"
                ], 400);
            }

            $profileName = $profile->display_name;
            $profile->delete();

            Log::info('Commission profile deleted', [
                'profile_id' => $id,
                'name' => $profileName,
                'deleted_by' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم حذف ملف العمولة بنجاح'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to delete commission profile', [
                'profile_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في حذف ملف العمولة: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Assign commission profile to a restaurant
     */
    public function assignProfileToRestaurant(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'restaurant_id' => 'required|exists:users,id',
            'commission_profile_id' => 'required|exists:commission_profiles,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $restaurant = UnifiedUser::findOrFail($request->restaurant_id);
            $commissionProfile = CommissionProfile::findOrFail($request->commission_profile_id);

            // Get or create restaurant pricing profile
            $restaurantProfile = RestaurantPricingProfile::firstOrCreate(
                ['restaurant_id' => $restaurant->id],
                [
                    'profile_name' => $restaurant->firstName . ' Profile',
                    'description' => 'Auto-generated profile for ' . $restaurant->firstName,
                    'is_active' => true
                ]
            );

            // Create or update the integration (direct restaurant to commission profile link)
            $integration = RestaurantPricingIntegration::updateOrCreate(
                ['restaurant_id' => $restaurant->id],
                [
                    'commission_profile_id' => $commissionProfile->id,
                    'integration_status' => 'active',
                    'assigned_by' => auth()->id() ?? 'system',
                    'assignment_method' => 'manual',
                    'assignment_reason' => 'Manual assignment via admin panel',
                    'effective_from' => now()
                ]
            );

            Log::info('Commission profile assigned to restaurant', [
                'restaurant_id' => $restaurant->id,
                'restaurant_name' => $restaurant->firstName,
                'commission_profile_id' => $commissionProfile->id,
                'profile_name' => $commissionProfile->display_name,
                'assigned_by' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم تعيين ملف العمولة بنجاح',
                'data' => [
                    'restaurant_id' => $restaurant->id,
                    'restaurant_name' => $restaurant->firstName,
                    'commission_profile_id' => $commissionProfile->id,
                    'profile_name' => $commissionProfile->display_name,
                    'integration_id' => $integration->id
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to assign commission profile to restaurant', [
                'restaurant_id' => $request->restaurant_id,
                'commission_profile_id' => $request->commission_profile_id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في تعيين ملف العمولة: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk operations for restaurants
     */
    public function bulkRestaurantOperations(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'operation' => 'required|in:assign_profile,activate_profiles,deactivate_profiles',
            'restaurant_ids' => 'required|array|min:1',
            'restaurant_ids.*' => 'exists:users,id',
            'profile_id' => 'required_if:operation,assign_profile|exists:commission_profiles,id',
            'reason' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $results = [
                'success' => 0,
                'failed' => 0,
                'errors' => []
            ];

            foreach ($request->restaurant_ids as $restaurantId) {
                try {
                    switch ($request->operation) {
                        case 'assign_profile':
                            $this->assignProfileToSingleRestaurant($restaurantId, $request->profile_id);
                            break;
                        case 'activate_profiles':
                            $this->activateRestaurantProfile($restaurantId);
                            break;
                        case 'deactivate_profiles':
                            $this->deactivateRestaurantProfile($restaurantId);
                            break;
                    }
                    $results['success']++;
                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['errors'][] = [
                        'restaurant_id' => $restaurantId,
                        'error' => $e->getMessage()
                    ];
                }
            }

            Log::info('Bulk restaurant operation completed', [
                'operation' => $request->operation,
                'total_restaurants' => count($request->restaurant_ids),
                'successful' => $results['success'],
                'failed' => $results['failed'],
                'performed_by' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => "تم تنفيذ العملية بنجاح على {$results['success']} مطعم",
                'results' => $results
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to perform bulk restaurant operation', [
                'operation' => $request->operation,
                'restaurant_ids' => $request->restaurant_ids,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في تنفيذ العملية المجمعة: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Helper method to assign profile to single restaurant
     */
    private function assignProfileToSingleRestaurant(string $restaurantId, string $profileId): void
    {
        $restaurant = UnifiedUser::findOrFail($restaurantId);
        $commissionProfile = CommissionProfile::findOrFail($profileId);

        // Get or create restaurant pricing profile
        $restaurantProfile = RestaurantPricingProfile::firstOrCreate(
            ['restaurant_id' => $restaurant->id],
            [
                'profile_name' => $restaurant->firstName . ' Profile',
                'description' => 'Auto-generated profile for ' . $restaurant->firstName,
                'is_active' => true
            ]
        );

        // Create or update the integration (direct restaurant to commission profile link)
        RestaurantPricingIntegration::updateOrCreate(
            ['restaurant_id' => $restaurant->id],
            [
                'commission_profile_id' => $commissionProfile->id,
                'integration_status' => 'active',
                'assigned_by' => auth()->id() ?? 'system',
                'assignment_method' => 'manual',
                'assignment_reason' => 'Bulk assignment via admin panel',
                'effective_from' => now()
            ]
        );
    }

    /**
     * Helper method to activate restaurant profile
     */
    private function activateRestaurantProfile(string $restaurantId): void
    {
        $restaurant = UnifiedUser::findOrFail($restaurantId);
        $restaurantProfile = $restaurant->restaurantPricingProfile;

        if ($restaurantProfile) {
            $restaurantProfile->update(['is_active' => true]);

            // Also activate the integration
            $integration = RestaurantPricingIntegration::where('restaurant_id', $restaurantId)->first();
            if ($integration) {
                $integration->update(['integration_status' => 'active']);
            }
        }
    }

    /**
     * Helper method to deactivate restaurant profile
     */
    private function deactivateRestaurantProfile(string $restaurantId): void
    {
        $restaurant = UnifiedUser::findOrFail($restaurantId);
        $restaurantProfile = $restaurant->restaurantPricingProfile;

        if ($restaurantProfile) {
            $restaurantProfile->update(['is_active' => false]);

            // Also deactivate the integration
            $integration = RestaurantPricingIntegration::where('restaurant_id', $restaurantId)->first();
            if ($integration) {
                $integration->update(['integration_status' => 'inactive']);
            }
        }
    }

    /**
     * Auto-assign commission profiles to restaurants
     */
    public function autoAssignProfiles(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'restaurant_ids' => 'nullable|array',
            'restaurant_ids.*' => 'uuid|exists:users,id',
            'force_reassign' => 'sometimes|boolean',
            'profile_type_filter' => 'sometimes|in:standard,premium,custom',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $result = $this->pricingService->autoAssignCommissionProfiles(
                $request->get('restaurant_ids'),
                $request->boolean('force_reassign', false),
                $request->get('profile_type_filter')
            );

            return response()->json([
                'success' => true,
                'message' => 'تم تعيين ملفات العمولة تلقائياً بنجاح',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to auto-assign profiles', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في تعيين ملفات العمولة تلقائياً',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk operations on restaurants
     */
    public function bulkOperations(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'operation' => 'required|in:assign_profile,activate_profiles,deactivate_profiles,delete_profiles',
            'restaurant_ids' => 'required|array|min:1',
            'restaurant_ids.*' => 'uuid|exists:users,id',
            'profile_id' => 'required_if:operation,assign_profile|uuid|exists:commission_profiles,id',
            'reason' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $result = $this->pricingService->performBulkOperation(
                $request->get('operation'),
                $request->get('restaurant_ids'),
                $request->get('profile_id'),
                $request->get('reason', 'Bulk operation by admin')
            );

            return response()->json([
                'success' => true,
                'message' => 'تم تنفيذ العملية المجمعة بنجاح',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to perform bulk operation', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في تنفيذ العملية المجمعة',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get restaurant performance metrics
     */
    public function getRestaurantMetrics(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'restaurant_id' => 'required|uuid|exists:users,id',
            'period' => 'sometimes|in:week,month,quarter,year',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after:start_date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $metrics = $this->pricingService->getRestaurantPerformanceMetrics(
                $request->get('restaurant_id'),
                $request->get('period', 'month'),
                $request->get('start_date'),
                $request->get('end_date')
            );

            return response()->json([
                'success' => true,
                'message' => 'تم جلب مقاييس الأداء بنجاح',
                'data' => $metrics
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get restaurant metrics', [
                'restaurant_id' => $request->get('restaurant_id'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في جلب مقاييس الأداء',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Assign commission profile to a restaurant
     */
    public function assignCommissionProfile(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'restaurant_id' => 'required|string|exists:users,id',
            'commission_profile_id' => 'required|string|exists:commission_profiles,id',
            'custom_profile_name' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $result = $this->pricingService->assignCommissionProfileToRestaurant(
                $request->restaurant_id,
                $request->commission_profile_id,
                $request->custom_profile_name
            );

            return response()->json([
                'success' => true,
                'message' => 'تم تعيين ملف العمولة للمطعم بنجاح',
                'data' => $result['data']
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to assign commission profile', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في تعيين ملف العمولة: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get commission profiles for bulk operations
     */
    public function getCommissionProfiles(): JsonResponse
    {
        try {
            $profiles = CommissionProfile::active()
                ->withCount('activeRestaurants')
                ->select(['id', 'display_name', 'description', 'profile_type', 'admin_commission_rate',
                         'driver_commission_rate', 'base_delivery_charge', 'per_km_delivery_rate', 'is_active'])
                ->orderBy('profile_type')
                ->orderBy('display_name')
                ->get()
                ->map(function ($profile) {
                    return [
                        'id' => $profile->id,
                        'display_name' => $profile->display_name,
                        'description' => $profile->description,
                        'profile_type' => $profile->profile_type,
                        'admin_commission_rate' => $profile->admin_commission_rate,
                        'driver_commission_rate' => $profile->driver_commission_rate,
                        'base_delivery_charge' => $profile->base_delivery_charge,
                        'per_km_delivery_rate' => $profile->per_km_delivery_rate,
                        'is_active' => $profile->is_active,
                        'restaurants_count' => $profile->active_restaurants_count ?? 0
                    ];
                });

            return response()->json([
                'success' => true,
                'message' => 'تم جلب ملفات العمولة بنجاح',
                'data' => $profiles
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get commission profiles', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في جلب ملفات العمولة',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
