<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create driver_wallets table
        Schema::connection('pgsql')->create('driver_wallets', function (Blueprint $table) {
            $table->uuid('id')->primary(); // UUID
            $table->uuid('driver_id')->index(); // Reference to users table
            $table->decimal('balance', 10, 2)->default(0); // Current balance
            $table->decimal('total_earned', 10, 2)->default(0); // Total earned amount
            $table->decimal('total_withdrawn', 10, 2)->default(0); // Total withdrawn amount
            $table->boolean('is_active')->default(true); // Wallet status
            $table->string('currency', 3)->default('SAR'); // Currency code
            $table->timestamp('last_transaction_at')->nullable(); // Last transaction timestamp
            $table->timestamps();

            // Foreign key constraint
            $table->foreign('driver_id')->references('id')->on('users')->onDelete('cascade');
            
            // Indexes for performance
            $table->index(['driver_id', 'is_active']);
            $table->index('last_transaction_at');
        });

        // Create driver_wallet_transactions table
        Schema::connection('pgsql')->create('driver_wallet_transactions', function (Blueprint $table) {
            $table->uuid('id')->primary(); // UUID
            $table->uuid('wallet_id')->index(); // Reference to driver_wallets
            $table->uuid('driver_id')->index(); // Reference to users table
            $table->enum('type', ['commission', 'withdrawal', 'adjustment', 'bonus', 'penalty'])->index();
            $table->decimal('amount', 10, 2); // Transaction amount
            $table->decimal('balance_before', 10, 2); // Balance before transaction
            $table->decimal('balance_after', 10, 2); // Balance after transaction
            $table->text('description'); // Transaction description
            $table->uuid('order_id')->nullable()->index(); // Related order ID
            $table->string('reference_id')->nullable()->index(); // External reference ID
            $table->enum('status', ['pending', 'completed', 'failed', 'cancelled'])->default('completed')->index();
            $table->json('metadata')->nullable(); // Additional transaction data
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('wallet_id')->references('id')->on('driver_wallets')->onDelete('cascade');
            $table->foreign('driver_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('order_id')->references('id')->on('orders')->onDelete('set null');
            
            // Indexes for performance
            $table->index(['driver_id', 'type', 'status']);
            $table->index(['wallet_id', 'created_at']);
            $table->index(['type', 'created_at']);
            $table->index('created_at');
        });

        echo "✅ Driver wallet tables created successfully\n";
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('pgsql')->dropIfExists('driver_wallet_transactions');
        Schema::connection('pgsql')->dropIfExists('driver_wallets');
        echo "✅ Driver wallet tables dropped\n";
    }
};
